using ColorConsole;
using Microsoft.Data.Sqlite;
using SQLitePCL;
using System;
using System.Collections.Generic;
using System.IO;

namespace BarrageGrab.Utils
{
    internal partial class Logger
    {
        ConsoleWriter console = new ConsoleWriter();

        public enum LogIndex
        {
            弹幕 = 0,
            礼物 = 1,
            特殊事件 = 2,
            程序日志 = 3,
            异常 = 4
        }
        public void LogToFile(string content, LogIndex index, long channel = 0)
        {
            var subDirectory = channel == 0 ? "" : $"{channel}/";
            var logPath = $"logs/{DateTime.Now.ToLongDateString()}/{subDirectory}{DateTime.Now.ToLongDateString()}_{index}.txt";
            AppendToFile($"{DateTime.Now.ToLongTimeString()} - {content}", logPath);
        }

        public void LogToConsole(string content, ConsoleColor color)
        {
            console.WriteLine($"{DateTime.Now.ToLongTimeString()} - {content}", color);
        }


        private static readonly object _fileLock = new object();
        private void AppendToFile(string content, string filePath)
        {
            try
            {
                // Create the directory if it doesn't exist
                string directoryPath = Path.GetDirectoryName(filePath);
                if (!Directory.Exists(directoryPath))
                {
                    Directory.CreateDirectory(directoryPath);
                }

                // Create the file if it doesn't exist
                if (!File.Exists(filePath))
                {
                    using (File.Create(filePath))
                    {
                        // The using block ensures the file is closed and resources are released.
                    }
                }

                // Append the content to the file
                lock (_fileLock)
                {
                    using (StreamWriter writer = File.AppendText(filePath))
                    {
                        writer.WriteLine($"{content}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ {DateTime.Now.ToLongDateString()} ]  {DateTime.Now.ToLongTimeString()} - Error appending to file:" + ex.Message);
            }
        }
    }

    internal partial class Logger
    {
        private readonly string connectionString;
        // Static lock object to ensure thread safety for database operations
        private static readonly object _dbLock = new object();

        public Logger(string connStr = null)
        {
            if (connStr == null) return;

            connectionString = connStr;

            try
            {
                // Initialize SQLitePCL batteries
                Batteries.Init();
                
                InitializeDatabase();
                var logMessage = "Connected to SQLite DB";
                LogToConsole(logMessage, ConsoleColor.Blue);
                LogToFile(logMessage, Logger.LogIndex.程序日志);
            }
            catch (Exception ex)
            {
                var logNoConnMessage = $"Unable to initialize SQLite DB: {ex.Message}";
                LogToConsole(logNoConnMessage, ConsoleColor.Magenta);
                LogToFile(logNoConnMessage, Logger.LogIndex.程序日志);
            }
        }

        private void InitializeDatabase()
        {
            // Ensure the Data directory exists
            var dataDir = Path.GetDirectoryName(connectionString.Replace("Data Source=", "").Split(';')[0]);
            if (!string.IsNullOrEmpty(dataDir) && !Directory.Exists(dataDir))
            {
                Directory.CreateDirectory(dataDir);
            }

            using (var connection = new SqliteConnection(connectionString))
            {
                connection.Open();

                // Create chat_log table
                var createChatLogTable = @"
                    CREATE TABLE IF NOT EXISTS chat_log (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        sender TEXT NOT NULL,
                        sender_id TEXT NOT NULL,
                        content TEXT NOT NULL,
                        time_stamp DATETIME NOT NULL
                    )";

                using (var command = new SqliteCommand(createChatLogTable, connection))
                {
                    command.ExecuteNonQuery();
                }

                // Create gift_log table
                var createGiftLogTable = @"
                    CREATE TABLE IF NOT EXISTS gift_log (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        sender TEXT NOT NULL,
                        sender_id TEXT NOT NULL,
                        receiver TEXT,
                        gift_name TEXT NOT NULL,
                        value INTEGER NOT NULL,
                        count INTEGER NOT NULL,
                        time_stamp DATETIME NOT NULL
                    )";

                using (var command = new SqliteCommand(createGiftLogTable, connection))
                {
                    command.ExecuteNonQuery();
                }
            }
        }

        private void ExecuteQuery(string query, IDictionary<string, object> parameters = null)
        {
            if (string.IsNullOrEmpty(connectionString)) return;

            // Use lock to ensure thread safety for database operations
            lock (_dbLock)
            {
                try
                {
                    using (var connection = new SqliteConnection(connectionString))
                    {
                        connection.Open();

                        using (var command = new SqliteCommand(query, connection))
                        {
                            // Set a reasonable timeout
                            command.CommandTimeout = 30;

                            // If there are parameters, add them to the command
                            if (parameters != null)
                            {
                                foreach (var param in parameters)
                                {
                                    command.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                                }
                            }

                            command.ExecuteNonQuery(); // Execute the command
                        }
                    }
                }
                catch (SqliteException ex)
                {
                    var errLogMessage = $"Error executing SQLite query: {ex.Message}";
                    LogToConsole(errLogMessage, ConsoleColor.Red);
                    LogToFile(errLogMessage, LogIndex.程序日志);
                }
                catch (Exception ex)
                {
                    var errLogMessage = $"Unexpected database error: {ex.Message}";
                    LogToConsole(errLogMessage, ConsoleColor.Red);
                    LogToFile(errLogMessage, LogIndex.程序日志);
                }
            }
        }


        public void LogChatToDb(ChatLog chatLog)
        {
            var query = "INSERT INTO chat_log (sender, sender_id, content, time_stamp) VALUES (@Sender, @SenderId, @Content, @TimeStamp);";

            var parameters = new Dictionary<string, object>
            {
                { "@Sender", chatLog.Sender },
                { "@SenderId", chatLog.SenderId },
                { "@Content", chatLog.Content },
                { "@TimeStamp", chatLog.TimeStamp }
            };

            ExecuteQuery(query, parameters);
        }

        public void LogGiftToDb(GiftLog giftLog)
        {
            var query = "INSERT INTO gift_log (sender, sender_id, receiver, gift_name, value, count, time_stamp) VALUES (@Sender, @SenderId, @Receiver, @GiftName, @Value, @Count, @TimeStamp);";

            var parameters = new Dictionary<string, object>
            {
                { "@Sender", giftLog.Sender },
                { "@SenderId", giftLog.SenderId },
                { "@Receiver", giftLog.Receiver },
                { "@GiftName", giftLog.GiftName },
                { "@Value", giftLog.Value },
                { "@Count", giftLog.Count },
                { "@TimeStamp", giftLog.TimeStamp }
            };

            ExecuteQuery(query, parameters);
        }
    }
}
