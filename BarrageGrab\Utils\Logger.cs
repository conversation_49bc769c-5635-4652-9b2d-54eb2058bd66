﻿using ColorConsole;
using MySql.Data.MySqlClient;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;

namespace BarrageGrab.Utils
{
    internal partial class Logger
    {
        ConsoleWriter console = new ConsoleWriter();

        public enum LogIndex
        {
            弹幕 = 0,
            礼物 = 1,
            特殊事件 = 2,
            程序日志 = 3,
            异常 = 4
        }
        public void LogToFile(string content, LogIndex index, long channel = 0)
        {
            var subDirectory = channel == 0 ? "" : $"{channel}/";
            var logPath = $"logs/{DateTime.Now.ToLongDateString()}/{subDirectory}{DateTime.Now.ToLongDateString()}_{index}.txt";
            AppendToFile($"{DateTime.Now.ToLongTimeString()} - {content}", logPath);
        }

        public void LogToConsole(string content, ConsoleColor color)
        {
            console.WriteLine($"{DateTime.Now.ToLongTimeString()} - {content}", color);
        }


        private static readonly object _fileLock = new object();
        private void AppendToFile(string content, string filePath)
        {
            try
            {
                // Create the directory if it doesn't exist
                string directoryPath = Path.GetDirectoryName(filePath);
                if (!Directory.Exists(directoryPath))
                {
                    Directory.CreateDirectory(directoryPath);
                }

                // Create the file if it doesn't exist
                if (!File.Exists(filePath))
                {
                    using (File.Create(filePath))
                    {
                        // The using block ensures the file is closed and resources are released.
                    }
                }

                // Append the content to the file
                lock (_fileLock)
                {
                    using (StreamWriter writer = File.AppendText(filePath))
                    {
                        writer.WriteLine($"{content}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ {DateTime.Now.ToLongDateString()} ]  {DateTime.Now.ToLongTimeString()} - Error appending to file:" + ex.Message);
            }
        }
    }

    internal partial class Logger
    {
        // TODO Add database logability
        MySqlConnection conn = null;
        int resetErrorCount = 0;
        // Static lock object to ensure thread safety for database operations
        private static readonly object _dbLock = new object();
        public Logger(string connStr = null)
        {
            if (connStr == null) return;
            conn = new MySqlConnection(connStr);
            conn.Open();
            if (conn.State == ConnectionState.Open)
            {
                var logMessage = "Connected to DB";
                LogToConsole(logMessage, ConsoleColor.Blue);
                LogToFile(logMessage, Logger.LogIndex.程序日志);
            }
            else
            {
                var logNoConnMessage = $"Unable to connect to DB. Current state: {conn.State}";
                LogToConsole(logNoConnMessage, ConsoleColor.Magenta);
                LogToFile(logNoConnMessage, Logger.LogIndex.程序日志);
            }
        }

        private void ResetClient()
        {
            try
            {
                if (conn != null)
                {
                    // Attempt to close any active readers
                    using (var cmd = new MySqlCommand("SELECT 1", conn))
                    {
                        cmd.CommandTimeout = 5;
                        try
                        {
                            if (conn.State == ConnectionState.Open)
                            {
                                cmd.ExecuteNonQuery();
                            }
                        }
                        catch { /* Ignore errors during reset attempt */ }
                    }

                    // Close and dispose the old connection
                    if (conn.State != ConnectionState.Closed)
                    {
                        conn.Close();
                    }
                    var lastConnStr = conn.ConnectionString;
                    conn.Dispose();

                    // Create a new connection
                    conn = new MySqlConnection(lastConnStr);
                    conn.Open();

                    var logMessage = "DB connection successfully reset";
                    LogToConsole(logMessage, ConsoleColor.Green);
                    LogToFile(logMessage, LogIndex.程序日志);
                }
            }
            catch (Exception ex)
            {
                var logMessage = $"Failed to reset DB connection: {ex.Message}";
                LogToConsole(logMessage, ConsoleColor.Red);
                LogToFile(logMessage, LogIndex.程序日志);
            }
        }

        private void ExecuteQuery(string query, IDictionary<string, object> parameters = null)
        {
            if (conn == null) return;

            // Always use a fresh connection for each query to avoid the "already open DataReader" issue
            bool shouldReopen = false;

            // Use lock to ensure only one thread executes SQL at a time
            lock (_dbLock)
            {
                try
                {
                    // Check if connection needs to be opened
                    if (conn.State == ConnectionState.Closed)
                    {
                        shouldReopen = true;
                        conn.Open();
                    }

                    using (MySqlCommand cmd = new MySqlCommand(query, conn))
                    {
                        // Set a reasonable timeout
                        cmd.CommandTimeout = 30;

                        // If there are parameters, add them to the command
                        if (parameters != null)
                        {
                            foreach (var param in parameters)
                            {
                                cmd.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                            }
                        }

                        cmd.ExecuteNonQuery(); // Execute the command
                    }
                    resetErrorCount = 0;
                }
                catch (MySqlException ex)
                {
                    var errLogMessage = $"Error executing SQL: {ex.Message}";
                    LogToConsole(errLogMessage, ConsoleColor.Red);
                    LogToFile(errLogMessage, LogIndex.程序日志);

                    // If we get the DataReader error, force a connection reset
                    if (ex.Message.Contains("open DataReader") && resetErrorCount < 10)
                    {
                        var resetMessage = $"DataReader conflict detected. Resetting SQL connection, current count: {resetErrorCount}";
                        LogToConsole(resetMessage, ConsoleColor.Yellow);
                        LogToFile(resetMessage, LogIndex.程序日志);
                        ResetClient();
                        resetErrorCount++;

                        // Try again with the reset connection if we haven't exceeded retry count
                        if (resetErrorCount < 3)
                        {
                            // Make a recursive call outside the lock to avoid deadlock
                            // The lock will be reacquired when the method is called again
                        }
                    }
                    else if (resetErrorCount < 10)
                    {
                        try
                        {
                            var resetMessage = $"Try to reset SQL connection, current reset count: {resetErrorCount}";
                            LogToConsole(resetMessage, ConsoleColor.Blue);
                            LogToFile(resetMessage, LogIndex.程序日志);
                            ResetClient();
                            resetErrorCount++;
                        }
                        catch (Exception exReset)
                        {
                            resetErrorCount++;
                            var errResetLogMessage = $"Error Resetting SQL Connection: {exReset.Message}";
                            LogToConsole(errResetLogMessage, ConsoleColor.Red);
                            LogToFile(errResetLogMessage, LogIndex.程序日志);
                        }
                    }
                }
                catch (Exception ex)
                {
                    var errLogMessage = $"Unexpected error: {ex.Message}";
                    LogToConsole(errLogMessage, ConsoleColor.Red);
                    LogToFile(errLogMessage, LogIndex.程序日志);
                }
                finally
                {
                    // If we explicitly opened the connection in this method, close it
                    if (shouldReopen && conn != null && conn.State != ConnectionState.Closed)
                    {
                        try
                        {
                            conn.Close();
                        }
                        catch { /* Ignore errors during cleanup */ }
                    }
                }
            } // End of lock

            // Handle retry outside the lock to avoid deadlock
            if (resetErrorCount > 0 && resetErrorCount < 3)
            {
                // If we've reset the connection and need to retry, do it outside the lock
                ExecuteQuery(query, parameters);
            }
        }


        public void LogChatToDb(ChatLog chatLog)
        {
            var query = "INSERT INTO `520`.chat_log (sender, sender_id, content, time_stamp) VALUES (@Sender, @SenderId, @Content, @TimeStamp);";

            var parameters = new Dictionary<string, object>
            {
                { "@Sender", chatLog.Sender },
                { "@SenderId", chatLog.SenderId },
                { "@Content", chatLog.Content },
                { "@TimeStamp", chatLog.TimeStamp }
            };

            ExecuteQuery(query, parameters);
        }

        public void LogGiftToDb(GiftLog giftLog)
        {
            var query = "INSERT INTO `520`.gift_log (sender, sender_id, receiver, gift_name, value, count, time_stamp) VALUES (@Sender, @SenderId, @Receiver, @GiftName, @Value, @Count, @TimeStamp);";

            var parameters = new Dictionary<string, object>
            {
                { "@Sender", giftLog.Sender },
                { "@SenderId", giftLog.SenderId },
                { "@Receiver", giftLog.Receiver },
                { "@GiftName", giftLog.GiftName },
                { "@Value", giftLog.Value },
                { "@Count", giftLog.Count },
                { "@TimeStamp", giftLog.TimeStamp }
            };

            ExecuteQuery(query, parameters);
        }
    }
}
