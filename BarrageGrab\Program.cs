using BarrageGrab.Utils;
using System;
using System.Runtime.InteropServices;

namespace BarrageGrab
{
    public class Program
    {
        private delegate bool ControlCtrlDelegate(int CtrlType);

        // Windows Error Reporting APIs
        [DllImport("kernel32.dll")]
        static extern uint SetErrorMode(uint uMode);

        [DllImport("kernel32.dll")]
        static extern bool SetProcessUserModeExceptionPolicy(uint dwFlags);

        [DllImport("kernel32.dll")]
        static extern bool GetProcessUserModeExceptionPolicy(out uint dwFlags);

        [DllImport("wer.dll", CharSet = CharSet.Unicode)]
        static extern int WerSetFlags(uint dwFlags);

        [DllImport("wer.dll", CharSet = CharSet.Unicode)]
        static extern int WerAddExcludedApplication(string pwzExeName, bool bAllUsers);

        // Error modes for SetErrorMode
        private const uint SEM_FAILCRITICALERRORS = 0x0001;
        private const uint SEM_NOALIGNMENTFAULTEXCEPT = 0x0004;
        private const uint SEM_NOGPFAULTERRORBOX = 0x0002;
        private const uint SEM_NOOPENFILEERRORBOX = 0x8000;

        // Process exception policies
        private const uint PROCESS_CALLBACK_FILTER_ENABLED = 0x1;

        // WER flags
        private const uint WER_FAULT_REPORTING_FLAG_NOHEAP = 0x1;
        private const uint WER_FAULT_REPORTING_FLAG_QUEUE = 0x2;
        private const uint WER_FAULT_REPORTING_FLAG_DISABLE_THREAD_SUSPENSION = 0x4;
        private const uint WER_FAULT_REPORTING_FLAG_QUEUE_UPLOAD = 0x8;

        private static Logger logger = new Logger();

        static WsBarrageService server = null;
        static void Main(string[] args)
        {
            // Suppress Windows Error Reporting
            DisableWindowsErrorReporting();

            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
            ConsoleHelper.DisableQuickEditMode();
            SetConsoleCtrlHandler(cancelHandler, true);//捕获控制台关闭
            Console.Title = "抖音弹幕监听推送";

            server = new WsBarrageService();
            server.StartListen();

            while (true)
            {
                var input = Console.ReadKey();
                switch (input.Key)
                {
                    case ConsoleKey.Escape: goto end;
                }
            }

        end:
            server.Close();
            Console.ReadKey();
        }

        private static void DisableWindowsErrorReporting()
        {
            try
            {
                // Method 1: Set the error mode to disable Windows Error Reporting popups
                var oldErrorMode = SetErrorMode(SEM_FAILCRITICALERRORS | SEM_NOGPFAULTERRORBOX | SEM_NOOPENFILEERRORBOX);

                // Method 2: Set process exception policy (Windows 7+)
                try
                {
                    uint oldPolicy;
                    if (GetProcessUserModeExceptionPolicy(out oldPolicy))
                    {
                        SetProcessUserModeExceptionPolicy(oldPolicy | PROCESS_CALLBACK_FILTER_ENABLED);
                    }
                }
                catch
                {
                    // This API might not exist on older Windows versions, so we just ignore any failures
                }

                // Method 3: Use WER APIs to disable reporting for this process (Windows Vista+)
                try
                {
                    // Disable WER for this process
                    WerSetFlags(WER_FAULT_REPORTING_FLAG_DISABLE_THREAD_SUSPENSION | WER_FAULT_REPORTING_FLAG_NOHEAP);

                    // Add this application to WER exclusion list
                    var exeName = System.IO.Path.GetFileName(System.Reflection.Assembly.GetExecutingAssembly().Location);
                    WerAddExcludedApplication(exeName, false);
                }
                catch
                {
                    // WER APIs might not be available, continue without them
                }

                // Method 4: Set environment variable to disable WER (backup method)
                try
                {
                    Environment.SetEnvironmentVariable("COMPlus_DbgJITDebugLaunchSetting", "0");
                }
                catch
                {
                    // Ignore if we can't set environment variable
                }

                logger.LogToFile("Windows Error Reporting has been comprehensively disabled for Windows Server 2012 R2", Logger.LogIndex.程序日志);
            }
            catch (Exception ex)
            {
                logger.LogToFile($"Failed to disable Windows Error Reporting: {ex.Message}", Logger.LogIndex.异常);
            }
        }

        private static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            Exception ex = (Exception)e.ExceptionObject;
            var msg = $"{DateTime.Now.ToLongTimeString()} - {ex.Message}\n {ex.StackTrace} ";
            logger.LogToFile(msg, Logger.LogIndex.异常);
        }

        [DllImport("kernel32.dll")]
        private static extern bool SetConsoleCtrlHandler(ControlCtrlDelegate HandlerRoutine, bool Add);
        private static ControlCtrlDelegate cancelHandler = new ControlCtrlDelegate(HandlerRoutine);

        public static bool HandlerRoutine(int CtrlType)
        {
            switch (CtrlType)
            {
                case 0:
                    //Console.WriteLine("0工具被强制关闭"); //Ctrl+C关闭  
                    //server.Close();
                    break;
                case 2:
                    Console.WriteLine("2工具被强制关闭");//按控制台关闭按钮关闭
                    server.Close();
                    break;
            }
            return false;
        }
    }

    public static class ConsoleHelper
    {
        [DllImport("kernel32.dll")]
        static extern bool GetConsoleMode(IntPtr hConsoleHandle, out uint lpMode);

        [DllImport("kernel32.dll")]
        static extern bool SetConsoleMode(IntPtr hConsoleHandle, uint dwMode);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr GetStdHandle(int nStdHandle);

        public static void DisableQuickEditMode()
        {
            IntPtr consoleHandle = GetStdHandle(-11); // STD_INPUT_HANDLE
            uint consoleMode;

            // Get the current console mode
            if (!GetConsoleMode(consoleHandle, out consoleMode))
            {
                Console.Error.WriteLine("Failed to get console mode.");
                return;
            }

            // Clear the ENABLE_QUICK_EDIT_MODE flag
            consoleMode &= ~(uint)0x0040; // ENABLE_QUICK_EDIT_MODE

            // Set the new console mode
            if (!SetConsoleMode(consoleHandle, consoleMode))
            {
                Console.Error.WriteLine("Failed to set console mode.");
                return;
            }
        }
    }
}

