﻿using BarrageGrab.JsonEntity;
using System;
using System.Linq;
using System.Threading;
using BarrageGrab.Utils;
using static System.Configuration.ConfigurationManager;

namespace BarrageGrab
{
    internal class Appsetting
    {
        private static readonly Appsetting ins = new Appsetting();

        Logger logger = new Logger();

        private static Timer ReloadConfigTimer { get; set; }

        public static Appsetting Current { get => ins; }

        public Appsetting()
        {
            ExecuteAndReschedule(null);   
        }

        void ReadConfig()
        {
            try
            {
                var config = OpenExeConfiguration(System.Configuration.ConfigurationUserLevel.None);
                ProcessFilter = config.AppSettings.Settings["processFilter"].Value.Trim().Split(',');
                WsProt = int.Parse(config.AppSettings.Settings["wsListenPort"].Value);
                PrintBarrage = config.AppSettings.Settings["printBarrage"].Value.ToLower() == "true";
                ProxyPort = int.Parse(config.AppSettings.Settings["proxyPort"].Value);
                PrintFilter = Enum.GetValues(typeof(BarrageMsgType)).Cast<int>().ToArray();
                FilterHostName = bool.Parse(config.AppSettings.Settings["filterHostName"].Value.Trim());
                HostNameFilter = config.AppSettings.Settings["hostNameFilter"].Value.Trim().Split(',').Where(w => !string.IsNullOrWhiteSpace(w)).ToArray();
                RoomIds = config.AppSettings.Settings["roomIds"].Value.Trim().Split(',').Where(w => !string.IsNullOrWhiteSpace(w)).Select(s => long.Parse(s)).ToArray();
                IdleTimeOut = int.Parse(config.AppSettings.Settings["idleTimeOut"].Value);
                WebResetScriptPath = config.AppSettings.Settings["webResetScriptPath"].Value;
                ActiveHourStart = int.Parse(config.AppSettings.Settings["activeHourStart"].Value);
                ActiveHourEnd = int.Parse(config.AppSettings.Settings["activeHourEnd"].Value);
                DbConnStr = config.AppSettings.Settings["dbConnStr"].Value;

                var printFilter = config.AppSettings.Settings["printFilter"].Value.Trim().ToLower();
                if (printFilter != "all")
                {
                    if (string.IsNullOrWhiteSpace(printFilter)) PrintFilter = new int[0];
                    else PrintFilter = printFilter.Split(',').Select(x => int.Parse(x)).ToArray();
                }
                var logMessage = $"Config loaded";
                logger.LogToConsole(logMessage, ConsoleColor.Yellow);
                logger.LogToFile(logMessage, Logger.LogIndex.程序日志);
            }
            catch (Exception ex)
            {
                var readConfigExceptionLogMessage = $"Error reading configs: {ex.Message}";
                logger.LogToConsole(readConfigExceptionLogMessage, ConsoleColor.Yellow);
                logger.LogToFile(readConfigExceptionLogMessage, Logger.LogIndex.程序日志);
                throw ex;
            }
        }

        void ExecuteAndReschedule(object state)
        {
            // Placeholder function logic here
            ReadConfig();

            // Reschedule the next execution
            ScheduleDailyTask(ExecuteAndReschedule);
        }

        static void ScheduleDailyTask(TimerCallback callback)
        {
            var now = DateTime.Now;
            var nextRunTime = now.Date.AddDays(1); // Schedule for 0:00 next day
            var timeToWait = nextRunTime - now;

            // Create a timer that triggers the callback at the specified time
            ReloadConfigTimer = new Timer(callback, null, timeToWait, Timeout.InfiniteTimeSpan);
        }

        /// <summary>
        /// 过滤的进程
        /// </summary>
        public string[] ProcessFilter { get; private set; }

        /// <summary>
        /// 端口号
        /// </summary>
        public int WsProt { get; private set; }

        /// <summary>
        /// 控制台打印消息开关
        /// </summary>
        public bool PrintBarrage { get; private set; }

        /// <summary>
        /// 代理端口
        /// </summary>
        public int ProxyPort { get; private set; } = 8827;

        /// <summary>
        /// 控制台输出过滤器
        /// </summary>
        public int[] PrintFilter { get; private set; }

        /// <summary>
        /// 监听的房间号
        /// </summary>
        public long[] RoomIds { get; private set; } = new long[0];

        /// <summary>
        /// 使用域名过滤
        /// </summary>
        public bool FilterHostName { get; private set; } = true;

        /// <summary>
        /// 域名白名单列表
        /// </summary>
        public string[] HostNameFilter { get; private set; } = new string[0];

        /// <summary>
        /// 假死检测超时
        /// </summary>
        public int IdleTimeOut { get; private set; } = 30;

        /// <summary>
        /// 重置运行批处理文件位置
        /// </summary>
        public string WebResetScriptPath { get; private set; }

        /// <summary>
        /// 活跃时间开始(包括)
        /// </summary>
        public int ActiveHourStart { get; private set; }

        /// <summary>
        /// 活跃时间结束(不包括)
        /// </summary>
        public int ActiveHourEnd { get; private set; }

        /// <summary>
        /// 数据库连接字符串
        /// </summary>
        public string DbConnStr { get; private set; }
    }
}
