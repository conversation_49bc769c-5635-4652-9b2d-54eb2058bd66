﻿using BarrageGrab.ProtoEntity;
using BarrageGrab.Proxy;
using BarrageGrab.Proxy.ProxyEventArgs;
using ColorConsole;
using ProtoBuf;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Threading;

namespace BarrageGrab
{
    /// <summary>
    /// 本机Wss弹幕抓取器
    /// </summary>
    public class WssBarrageGrab : IDisposable
    {
        //ISystemProxy proxy = new FiddlerProxy();
        ISystemProxy proxy;
        Appsetting appsetting = Appsetting.Current;
        ConsoleWriter console = new ConsoleWriter();
        //解包成功的域名缓存
        List<string> succPackHostNames = new List<string>();
        Utils.Logger logger = new Utils.Logger();

        bool http = false;

        //已知的弹幕域名服务器
        string[] wsHostNames = Appsetting.Current.HostNameFilter;

        /// <summary>
        /// 进入直播间
        /// </summary>
        public event EventHandler<MemberMessage> OnMemberMessage;

        /// <summary>
        /// 关注
        /// </summary>
        public event EventHandler<SocialMessage> OnSocialMessage;

        /// <summary>
        /// 聊天
        /// </summary>
        public event EventHandler<ChatMessage> OnChatMessage;

        /// <summary>
        /// 点赞
        /// </summary>
        public event EventHandler<LikeMessage> OnLikeMessage;

        /// <summary>
        /// 礼物
        /// </summary>
        public event EventHandler<GiftMessage> OnGiftMessage;

        /// <summary>
        /// 直播间统计
        /// </summary>
        public event EventHandler<RoomUserSeqMessage> OnRoomUserSeqMessage;

        /// <summary>
        /// 直播间状态变更
        /// </summary>
        public event EventHandler<ControlMessage> OnControlMessage;

        /// <summary>
        /// 粉丝团消息
        /// </summary>
        public event EventHandler<FansclubMessage> OnFansclubMessage;

        public WssBarrageGrab()
        {
            // Initialize TitaniumProxy with error handling
            try
            {
                proxy = new TitaniumProxy();
                proxy.OnWebSocketData += Proxy_OnWebSocketData;
                proxy.OnFetchResponse += Proxy_OnFetchResponse;
            }
            catch (Exception ex)
            {
                logger.LogToFile($"Error initializing TitaniumProxy: {ex.Message}", Utils.Logger.LogIndex.异常);
                // Attempt to recover by creating a new instance after a short delay
                Thread.Sleep(2000);
                try
                {
                    proxy = new TitaniumProxy();
                    proxy.OnWebSocketData += Proxy_OnWebSocketData;
                    proxy.OnFetchResponse += Proxy_OnFetchResponse;
                }
                catch (Exception retryEx)
                {
                    logger.LogToFile($"Failed to recover TitaniumProxy: {retryEx.Message}", Utils.Logger.LogIndex.异常);
                    // Use a fallback proxy implementation or provide clear error message
                    console.WriteLine("Failed to initialize proxy service. Application may have limited functionality.", ConsoleColor.Red);
                }
            }
        }

        public void Start()
        {
            try
            {
                if (proxy != null)
                {
                    proxy.Start();
                }
                else
                {
                    logger.LogToFile("Cannot start proxy as it was not initialized properly", Utils.Logger.LogIndex.异常);
                }
            }
            catch (Exception ex)
            {
                logger.LogToFile($"Error starting proxy: {ex.Message}", Utils.Logger.LogIndex.异常);
            }
        }

        public void Dispose()
        {
            try
            {
                if (proxy != null)
                {
                    proxy.Dispose();
                }
            }
            catch (Exception ex)
            {
                logger.LogToFile($"Error disposing proxy: {ex.Message}", Utils.Logger.LogIndex.异常);
            }
        }

        //gzip解压缩
        private byte[] Decompress(byte[] zippedData)
        {
            MemoryStream ms = new MemoryStream(zippedData);
            GZipStream compressedzipStream = new GZipStream(ms, CompressionMode.Decompress);
            MemoryStream outBuffer = new MemoryStream();
            byte[] block = new byte[1024];
            while (true)
            {
                int bytesRead = compressedzipStream.Read(block, 0, block.Length);
                if (bytesRead <= 0)
                    break;
                else
                    outBuffer.Write(block, 0, bytesRead);
            }
            compressedzipStream.Close();
            return outBuffer.ToArray();
        }

        //ws数据处理
        private void Proxy_OnWebSocketData(object sender, WsMessageEventArgs e)
        {
            if (!appsetting.ProcessFilter.Contains(e.ProcessName)) return;

            if (http)
            {
                var logMessage = $"{DateTime.Now.ToLongTimeString()} - Message channel switched to: Websocket";
                console.WriteLine(logMessage, ConsoleColor.Blue);
                logger.LogToFile(logMessage, Utils.Logger.LogIndex.程序日志);
                http = false;
            }

            var buff = e.Payload;
            if (buff.Length == 0) return;
            //如果需要Gzip解压缩，但是开头字节不符合Gzip特征字节 则不处理
            if (e.NeedDecompress && buff[0] != 0x08) return;

            try
            {
                var enty = Serializer.Deserialize<WssResponse>(new ReadOnlyMemory<byte>(buff));
                if (enty == null) return;

                //检测包格式
                if (!enty.Headers.Any(a => a.Key == "compress_type" && a.Value == "gzip")) return;

                byte[] allBuff;
                //解压gzip
                allBuff = e.NeedDecompress ? Decompress(enty.Payload) : enty.Payload;
                var response = Serializer.Deserialize<Response>(new ReadOnlyMemory<byte>(allBuff));

                if (!succPackHostNames.Contains(e.HostName))
                {
                    succPackHostNames.Add(e.HostName);
                    SaveHostNameCache();
                }
                response.Messages.ForEach(f => DoMessage(f));
            }
            catch (Exception) { }
        }

        //http 数据处理
        private void Proxy_OnFetchResponse(object sender, HttpResponseEventArgs e)
        {
            if (!http)
            {
                var logMessage = $"{DateTime.Now.ToLongTimeString()} - Message channel switched to: HTTP";
                console.WriteLine(logMessage, ConsoleColor.Blue);
                logger.LogToFile(logMessage, Utils.Logger.LogIndex.程序日志);
                http = true;
            }

            var payload = e.HttpClient.Response.Body;
            var response = Serializer.Deserialize<Response>(new ReadOnlyMemory<byte>(payload));

            if (!succPackHostNames.Contains(e.HostName))
            {
                succPackHostNames.Add(e.HostName);
                SaveHostNameCache();
            }
            response.Messages.ForEach(f =>
            {
                DoMessage(f);
            });
        }

        //用于缓存接收过的消息ID，判断是否重复接收
        Dictionary<string, List<long>> msgDic = new Dictionary<string, List<long>>();

        //发送事件
        private void DoMessage(Message msg)
        {
            List<long> msgIdList;
            if (msgDic.ContainsKey(msg.Method))
            {
                msgIdList = msgDic[msg.Method];
            }
            else
            {
                msgIdList = new List<long>(320);
                msgDic.Add(msg.Method, msgIdList);
            }
            if (msgIdList.Contains(msg.msgId))
            {
                return;
            }

            msgIdList.Add(msg.msgId);
            if (msgIdList.Count > 300) msgIdList.RemoveAt(0);

            try
            {
                switch (msg.Method)
                {
                    //来了
                    case "WebcastMemberMessage":
                        {
                            var arg = Serializer.Deserialize<MemberMessage>(new ReadOnlyMemory<byte>(msg.Payload));
                            this.OnMemberMessage?.Invoke(this, arg);
                            break;
                        }
                    //关注
                    case "WebcastSocialMessage":
                        {
                            var arg = Serializer.Deserialize<SocialMessage>(new ReadOnlyMemory<byte>(msg.Payload));
                            this.OnSocialMessage?.Invoke(this, arg);
                            break;
                        }
                    //消息
                    case "WebcastChatMessage":
                        {
                            var arg = Serializer.Deserialize<ChatMessage>(new ReadOnlyMemory<byte>(msg.Payload));
                            this.OnChatMessage?.Invoke(this, arg);
                            break;
                        }
                    //点赞
                    case "WebcastLikeMessage":
                        {
                            var arg = Serializer.Deserialize<LikeMessage>(new ReadOnlyMemory<byte>(msg.Payload));
                            this.OnLikeMessage?.Invoke(this, arg);
                            break;
                        }
                    //礼物
                    case "WebcastGiftMessage":
                        {
                            var arg = Serializer.Deserialize<GiftMessage>(new ReadOnlyMemory<byte>(msg.Payload));

                            if (arg.Gift == null)
                            {
                                if (arg.giftId == 685)
                                {
                                    arg.Gift = new ProtoEntity.GiftStruct()
                                    {
                                        Id = arg.giftId,
                                        Name = "粉丝灯牌",
                                        diamondCount = 1
                                    };
                                }
                                else if (arg.giftId == 3389)
                                {
                                    arg.Gift = new GiftStruct()
                                    {
                                        Id = arg.giftId,
                                        Name = "欢乐盲盒",
                                        diamondCount = 10
                                    };
                                }
                                else if (arg.giftId == 4021)
                                {
                                    arg.Gift = new GiftStruct()
                                    {
                                        Id = arg.giftId,
                                        Name = "欢乐拼图",
                                        diamondCount = 10
                                    };
                                }
                                else
                                {
                                    console.WriteLine("未能识别的礼物ID: " + arg.giftId, ConsoleColor.DarkYellow);
                                    break;
                                }
                            }

                            this.OnGiftMessage?.Invoke(this, arg);
                            break;
                        }
                    //直播间统计
                    case "WebcastRoomUserSeqMessage":
                        {
                            var arg = Serializer.Deserialize<RoomUserSeqMessage>(new ReadOnlyMemory<byte>(msg.Payload));
                            this.OnRoomUserSeqMessage?.Invoke(this, arg);
                            break;
                        }
                    //直播间状态变更
                    case "WebcastControlMessage":
                        {
                            var arg = Serializer.Deserialize<ControlMessage>(new ReadOnlyMemory<byte>(msg.Payload));
                            this.OnControlMessage?.Invoke(this, arg);
                            break;
                        }
                    //粉丝团消息
                    case "WebcastFansclubMessage":
                        {
                            var arg = Serializer.Deserialize<FansclubMessage>(new ReadOnlyMemory<byte>(msg.Payload));
                            this.OnFansclubMessage?.Invoke(this, arg);
                            break;
                        }
                    default:
                        break;
                }
            }
            catch (Exception)
            {
                return;
            }
        }

        //将成功解包的域名缓存到文件
        private void SaveHostNameCache()
        {
            //获取程序运行目录
            var baseDir = Directory.GetCurrentDirectory();
            var fullPath = Path.Combine(baseDir, "成功解包域名缓存.txt");

            try
            {
                //文件不存在则创建
                if (!File.Exists(fullPath))
                {
                    File.Create(fullPath);
                }
                var text = File.ReadAllText(fullPath);
                var currentHosts = text.Split('\n').Where(w => !string.IsNullOrWhiteSpace(w)).Select(s => s.Trim().Trim('\r')).ToList();
                var newHosts = succPackHostNames.Except(currentHosts).ToList();
                //保存
                if (newHosts.Count > 0)
                {
                    File.AppendAllLines(fullPath, newHosts);
                }
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"[ {DateTime.Now.ToLongDateString()} ]  {DateTime.Now.ToLongTimeString()} - Error write hosts to file:" + ex.Message);
            }
        }
    }
}
