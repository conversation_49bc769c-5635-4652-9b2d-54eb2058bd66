﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<!--配置更改后重启才能生效-->
	<appSettings>
		
		<!--过滤Websocket数据源进程,可用','进行分隔，程序将会监听以下进程的弹幕信息-->
		<add key="processFilter" value="直播伴侣,chrome,msedge,douyin" />
		
		<!--Websocket监听端口-->
		<add key="wsListenPort" value="8888" />
		<!--在控制台输出弹幕-->
		<add key="printBarrage" value="true" />
		<!--要在控制台打印的弹幕类型,可以用','隔开   all[全部]，1[普通弹幕]，2[点赞消息]，3[进入直播间]，4[关注消息]，5[礼物消息]，6[统计消息]，7[粉丝团消息]-->
		<add key="printFilter" value="all" />
		<!--系统代理端口-->
		<add key="proxyPort" value="8827" />
		<!--开启内置的域名过滤，设置为false会解包所有https请求，cpu占用很高，建议在无法获取弹幕数据时调整 -->
		<add key="filterHostName" value="true" />
		<!--已知的弹幕域名列表 ','分隔  用作过滤规则中，凡是webcast开头的域名程序都会自动列入白名单-->
		<add key="hostNameFilter" value="     webcast3-ws-web-hl.douyin.com,              webcast3-ws-web-lf.douyin.com,     webcast100-ws-web-lq.amemv.com,              frontier-im.douyin.com,   " />
		<!--要进行过滤的房间ID,不填代表监听所有，多项使用','分隔，浏览器进入直播间 F12 控制台输入 'window.localStorage.playRoom' 即可快速看到房间ID(不是地址栏中的那个) -->
		<add key="roomIds" value="" />
    <!--WS连接假死检测超时(秒)-->
    <add key="idleTimeOut" value="30" />
    <!--重置浏览器服务批处理路径-->
    <add key="webResetScriptPath" value="C:\Users\<USER>\Desktop\reset.bat" />
    <!--活跃时刻(开始）-->
    <add key="activeHourStart" value="10" />
    <!--活跃时刻(结束)-->
    <add key="activeHourEnd" value="1" />
    <add key="dbConnStr" value="server=************;user=Tesla;database=520_test;port=13306;password=****************;"/>
	</appSettings>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>