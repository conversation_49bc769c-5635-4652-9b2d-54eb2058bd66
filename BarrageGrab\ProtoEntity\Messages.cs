﻿// <auto-generated>
//   This file was generated by a tool; you should avoid making direct changes.
//   Consider using 'partial classes' to extend these types
//   Input: my.proto
// </auto-generated>



using System.Collections.Generic;

namespace BarrageGrab.ProtoEntity
{
#pragma warning disable CS0612, CS0618, CS1591, <PERSON>3021, <PERSON>E0079, IDE1006, RCS1036, RCS1057, RCS1085, RCS1192
    [global::ProtoBuf.ProtoContract()]
    public partial class Response : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        [global::ProtoBuf.ProtoMember(1, Name = @"messages")]
        public global::System.Collections.Generic.List<Message> Messages { get; } = new global::System.Collections.Generic.List<Message>();

        [global::ProtoBuf.ProtoMember(2, Name = @"cursor")]
        [global::System.ComponentModel.DefaultValue("")]
        public string Cursor { get; set; } = "";

        [global::ProtoBuf.ProtoMember(3)]
        public long fetchInterval { get; set; }

        [global::ProtoBuf.ProtoMember(4, Name = @"now")]
        public long Now { get; set; }

        [global::ProtoBuf.ProtoMember(5)]
        [global::System.ComponentModel.DefaultValue("")]
        public string internalExt { get; set; } = "";

        [global::ProtoBuf.ProtoMember(6)]
        public int fetchType { get; set; }

        [global::ProtoBuf.ProtoMember(7)]
        [global::ProtoBuf.ProtoMap]
        public global::System.Collections.Generic.Dictionary<string, string> routeParams { get; } = new global::System.Collections.Generic.Dictionary<string, string>();

        [global::ProtoBuf.ProtoMember(8)]
        public long heartbeatDuration { get; set; }

        [global::ProtoBuf.ProtoMember(9)]
        public bool needAck { get; set; }

        [global::ProtoBuf.ProtoMember(10)]
        [global::System.ComponentModel.DefaultValue("")]
        public string pushServer { get; set; } = "";

        [global::ProtoBuf.ProtoMember(11)]
        [global::System.ComponentModel.DefaultValue("")]
        public string liveCursor { get; set; } = "";

        [global::ProtoBuf.ProtoMember(12)]
        public bool historyNoMore { get; set; }

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class Message : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        [global::ProtoBuf.ProtoMember(1, Name = @"method")]
        [global::System.ComponentModel.DefaultValue("")]
        public string Method { get; set; } = "";

        [global::ProtoBuf.ProtoMember(2, Name = @"payload")]
        public byte[] Payload { get; set; }

        [global::ProtoBuf.ProtoMember(3)]
        public long msgId { get; set; }

        [global::ProtoBuf.ProtoMember(4)]
        public int msgType { get; set; }

        [global::ProtoBuf.ProtoMember(5, Name = @"offset")]
        public long Offset { get; set; }

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class RoomUserSeqMessage : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        [global::ProtoBuf.ProtoMember(1, Name = @"common")]
        public Common Common { get; set; }

        [global::ProtoBuf.ProtoMember(2, Name = @"ranks")]
        public global::System.Collections.Generic.List<Contributor> Ranks { get; } = new global::System.Collections.Generic.List<Contributor>();

        [global::ProtoBuf.ProtoMember(3, Name = @"total")]
        public long Total { get; set; }

        [global::ProtoBuf.ProtoMember(4)]
        [global::System.ComponentModel.DefaultValue("")]
        public string popStr { get; set; } = "";

        [global::ProtoBuf.ProtoMember(5, Name = @"seats")]
        public global::System.Collections.Generic.List<Contributor> Seats { get; } = new global::System.Collections.Generic.List<Contributor>();

        [global::ProtoBuf.ProtoMember(6, Name = @"popularity")]
        public long Popularity { get; set; }

        [global::ProtoBuf.ProtoMember(7)]
        public long totalUser { get; set; }

        [global::ProtoBuf.ProtoMember(8)]
        [global::System.ComponentModel.DefaultValue("")]
        public string totalUserStr { get; set; } = "";

        [global::ProtoBuf.ProtoMember(9)]
        [global::System.ComponentModel.DefaultValue("")]
        public string totalStr { get; set; } = "";

        [global::ProtoBuf.ProtoMember(10)]
        [global::System.ComponentModel.DefaultValue("")]
        public string onlineUserForAnchor { get; set; } = "";

        [global::ProtoBuf.ProtoMember(11)]
        [global::System.ComponentModel.DefaultValue("")]
        public string totalPvForAnchor { get; set; } = "";

        [global::ProtoBuf.ProtoContract()]
        public partial class Contributor : global::ProtoBuf.IExtensible
        {
            private global::ProtoBuf.IExtension __pbn__extensionData;
            global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
                => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

            [global::ProtoBuf.ProtoMember(1, Name = @"score")]
            public long Score { get; set; }

            [global::ProtoBuf.ProtoMember(2, Name = @"user")]
            public User User { get; set; }

            [global::ProtoBuf.ProtoMember(3, Name = @"rank")]
            public long Rank { get; set; }

            [global::ProtoBuf.ProtoMember(4, Name = @"delta")]
            public long Delta { get; set; }

            [global::ProtoBuf.ProtoMember(5)]
            public bool isHidden { get; set; }

            [global::ProtoBuf.ProtoMember(6)]
            [global::System.ComponentModel.DefaultValue("")]
            public string scoreDescription { get; set; } = "";

            [global::ProtoBuf.ProtoMember(7)]
            [global::System.ComponentModel.DefaultValue("")]
            public string exactlyScore { get; set; } = "";

        }

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class GiftMessage : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        [global::ProtoBuf.ProtoMember(1, Name = @"common")]
        public Common Common { get; set; }

        [global::ProtoBuf.ProtoMember(2)]
        public long giftId { get; set; }

        [global::ProtoBuf.ProtoMember(3)]
        public long fanTicketCount { get; set; }

        [global::ProtoBuf.ProtoMember(4)]
        public long groupCount { get; set; }

        [global::ProtoBuf.ProtoMember(5)]
        public long repeatCount { get; set; }

        [global::ProtoBuf.ProtoMember(6)]
        public long comboCount { get; set; }

        [global::ProtoBuf.ProtoMember(7, Name = @"user")]
        public User User { get; set; }

        [global::ProtoBuf.ProtoMember(8)]
        public User toUser { get; set; }

        [global::ProtoBuf.ProtoMember(9)]
        public int repeatEnd { get; set; }

        [global::ProtoBuf.ProtoMember(10)]
        public TextEffect textEffect { get; set; }

        [global::ProtoBuf.ProtoMember(11)]
        public long groupId { get; set; }

        [global::ProtoBuf.ProtoMember(12)]
        public long incomeTaskgifts { get; set; }

        [global::ProtoBuf.ProtoMember(13)]
        public long roomFanTicketCount { get; set; }

        [global::ProtoBuf.ProtoMember(14, Name = @"priority")]
        public GiftIMPriority Priority { get; set; }

        [global::ProtoBuf.ProtoMember(15, Name = @"gift")]
        public GiftStruct Gift { get; set; }

        [global::ProtoBuf.ProtoMember(16)]
        [global::System.ComponentModel.DefaultValue("")]
        public string logId { get; set; } = "";

        [global::ProtoBuf.ProtoMember(17)]
        public long sendType { get; set; }

        [global::ProtoBuf.ProtoMember(18)]
        public PublicAreaCommon publicAreaCommon { get; set; }

        [global::ProtoBuf.ProtoMember(19)]
        public Text trayDisplayText { get; set; }

        [global::ProtoBuf.ProtoMember(20)]
        public long bannedDisplayEffects { get; set; }

        [global::ProtoBuf.ProtoMember(21)]
        public GiftTrayInfo trayInfo { get; set; }

        [global::ProtoBuf.ProtoMember(24)]
        public AssetEffectMixInfo assetEffectMixInfo { get; set; }

        [global::ProtoBuf.ProtoContract()]
        public partial class TextEffect : global::ProtoBuf.IExtensible
        {
            private global::ProtoBuf.IExtension __pbn__extensionData;
            global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
                => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

            [global::ProtoBuf.ProtoMember(1, Name = @"portrait")]
            public Detail Portrait { get; set; }

            [global::ProtoBuf.ProtoMember(2, Name = @"landscape")]
            public Detail Landscape { get; set; }

            [global::ProtoBuf.ProtoContract()]
            public partial class Detail : global::ProtoBuf.IExtensible
            {
                private global::ProtoBuf.IExtension __pbn__extensionData;
                global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
                    => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

                [global::ProtoBuf.ProtoMember(1, Name = @"text")]
                public Text Text { get; set; }

                [global::ProtoBuf.ProtoMember(2)]
                public int textFontSize { get; set; }

                [global::ProtoBuf.ProtoMember(3, Name = @"background")]
                public Image Background { get; set; }

                [global::ProtoBuf.ProtoMember(4, Name = @"start")]
                public int Start { get; set; }

                [global::ProtoBuf.ProtoMember(5, Name = @"duration")]
                public int Duration { get; set; }

                [global::ProtoBuf.ProtoMember(6, Name = @"x")]
                public int X { get; set; }

                [global::ProtoBuf.ProtoMember(7, Name = @"y")]
                public int Y { get; set; }

                [global::ProtoBuf.ProtoMember(8, Name = @"width")]
                public int Width { get; set; }

                [global::ProtoBuf.ProtoMember(9, Name = @"height")]
                public int Height { get; set; }

                [global::ProtoBuf.ProtoMember(10)]
                public int shadowDx { get; set; }

                [global::ProtoBuf.ProtoMember(11)]
                public int shadowDy { get; set; }

                [global::ProtoBuf.ProtoMember(12)]
                public int shadowRadius { get; set; }

                [global::ProtoBuf.ProtoMember(13)]
                [global::System.ComponentModel.DefaultValue("")]
                public string shadowColor { get; set; } = "";

                [global::ProtoBuf.ProtoMember(14)]
                [global::System.ComponentModel.DefaultValue("")]
                public string strokeColor { get; set; } = "";

                [global::ProtoBuf.ProtoMember(15)]
                public int strokeWidth { get; set; }

            }

        }

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class LikeMessage : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        [global::ProtoBuf.ProtoMember(1, Name = @"common")]
        public Common Common { get; set; }

        [global::ProtoBuf.ProtoMember(2, Name = @"count")]
        public long Count { get; set; }

        [global::ProtoBuf.ProtoMember(3, Name = @"total")]
        public long Total { get; set; }

        [global::ProtoBuf.ProtoMember(4, Name = @"color")]
        public long Color { get; set; }

        [global::ProtoBuf.ProtoMember(5, Name = @"user")]
        public User User { get; set; }

        [global::ProtoBuf.ProtoMember(6, Name = @"icon")]
        [global::System.ComponentModel.DefaultValue("")]
        public string Icon { get; set; } = "";

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class ChatMessage : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        [global::ProtoBuf.ProtoMember(1, Name = @"common")]
        public Common Common { get; set; }

        [global::ProtoBuf.ProtoMember(2, Name = @"user")]
        public User User { get; set; }

        [global::ProtoBuf.ProtoMember(3, Name = @"content")]
        [global::System.ComponentModel.DefaultValue("")]
        public string Content { get; set; } = "";

        [global::ProtoBuf.ProtoMember(4)]
        public bool visibleToSender { get; set; }

        [global::ProtoBuf.ProtoMember(5)]
        public Image backgroundImage { get; set; }

        [global::ProtoBuf.ProtoMember(6)]
        [global::System.ComponentModel.DefaultValue("")]
        public string fullScreenTextColor { get; set; } = "";

        [global::ProtoBuf.ProtoMember(7)]
        public Image backgroundImageV2 { get; set; }

        [global::ProtoBuf.ProtoMember(9)]
        public PublicAreaCommon publicAreaCommon { get; set; }

        [global::ProtoBuf.ProtoMember(10)]
        public Image giftImage { get; set; }

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class SocialMessage : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        [global::ProtoBuf.ProtoMember(1, Name = @"common")]
        public Common Common { get; set; }

        [global::ProtoBuf.ProtoMember(2, Name = @"user")]
        public User User { get; set; }

        [global::ProtoBuf.ProtoMember(3)]
        public long shareType { get; set; }

        [global::ProtoBuf.ProtoMember(4, Name = @"action")]
        public long Action { get; set; }

        [global::ProtoBuf.ProtoMember(5)]
        [global::System.ComponentModel.DefaultValue("")]
        public string shareTarget { get; set; } = "";

        [global::ProtoBuf.ProtoMember(6)]
        public long followCount { get; set; }

        [global::ProtoBuf.ProtoMember(7)]
        public PublicAreaCommon publicAreaCommon { get; set; }

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class MemberMessage : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        [global::ProtoBuf.ProtoMember(1, Name = @"common")]
        public Common Common { get; set; }

        [global::ProtoBuf.ProtoMember(2, Name = @"user")]
        public User User { get; set; }

        [global::ProtoBuf.ProtoMember(3)]
        public long memberCount { get; set; }

        [global::ProtoBuf.ProtoMember(4, Name = @"operator")]
        public User Operator { get; set; }

        [global::ProtoBuf.ProtoMember(5)]
        public bool isSetToAdmin { get; set; }

        [global::ProtoBuf.ProtoMember(6)]
        public bool isTopUser { get; set; }

        [global::ProtoBuf.ProtoMember(7)]
        public long rankScore { get; set; }

        [global::ProtoBuf.ProtoMember(8)]
        public long topUserNo { get; set; }

        [global::ProtoBuf.ProtoMember(9)]
        public long enterType { get; set; }

        [global::ProtoBuf.ProtoMember(10, Name = @"action")]
        public long Action { get; set; }

        [global::ProtoBuf.ProtoMember(11)]
        [global::System.ComponentModel.DefaultValue("")]
        public string actionDescription { get; set; } = "";

        [global::ProtoBuf.ProtoMember(12)]
        public long userId { get; set; }

        [global::ProtoBuf.ProtoMember(13)]
        public EffectConfig effectConfig { get; set; }

        [global::ProtoBuf.ProtoMember(14)]
        [global::System.ComponentModel.DefaultValue("")]
        public string popStr { get; set; } = "";

        [global::ProtoBuf.ProtoMember(15)]
        public EffectConfig enterEffectConfig { get; set; }

        [global::ProtoBuf.ProtoMember(16)]
        public Image backgroundImage { get; set; }

        [global::ProtoBuf.ProtoMember(17)]
        public Image backgroundImageV2 { get; set; }

        [global::ProtoBuf.ProtoMember(18)]
        public Text anchorDisplayText { get; set; }

        [global::ProtoBuf.ProtoMember(19)]
        public PublicAreaCommon publicAreaCommon { get; set; }

        [global::ProtoBuf.ProtoContract()]
        public partial class EffectConfig : global::ProtoBuf.IExtensible
        {
            private global::ProtoBuf.IExtension __pbn__extensionData;
            global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
                => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

            [global::ProtoBuf.ProtoMember(1, Name = @"type")]
            public long Type { get; set; }

            [global::ProtoBuf.ProtoMember(2, Name = @"icon")]
            public Image Icon { get; set; }

            [global::ProtoBuf.ProtoMember(3)]
            public long avatarPos { get; set; }

            [global::ProtoBuf.ProtoMember(4, Name = @"text")]
            public Text Text { get; set; }

            [global::ProtoBuf.ProtoMember(5)]
            public Image textIcon { get; set; }

            [global::ProtoBuf.ProtoMember(6)]
            public int stayTime { get; set; }

            [global::ProtoBuf.ProtoMember(7)]
            public long animAssetId { get; set; }

            [global::ProtoBuf.ProtoMember(8, Name = @"badge")]
            public Image Badge { get; set; }

            [global::ProtoBuf.ProtoMember(9, Name = @"flexSettingArray", IsPacked = true)]
            public long[] flexSettingArrays { get; set; }

            [global::ProtoBuf.ProtoMember(10)]
            public Image textIconOverlay { get; set; }

            [global::ProtoBuf.ProtoMember(11)]
            public Image animatedBadge { get; set; }

            [global::ProtoBuf.ProtoMember(12)]
            public bool hasSweepLight { get; set; }

            [global::ProtoBuf.ProtoMember(13, Name = @"textFlexSettingArray", IsPacked = true)]
            public long[] textFlexSettingArrays { get; set; }

            [global::ProtoBuf.ProtoMember(14)]
            public long centerAnimAssetId { get; set; }

        }

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class ControlMessage : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        [global::ProtoBuf.ProtoMember(1, Name = @"common")]
        public Common Common { get; set; }

        /// <summary>
        /// 3 下播
        /// </summary>
        [global::ProtoBuf.ProtoMember(2, Name = @"status")]
        public int Status { get; set; }

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class FansclubMessage : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        [global::ProtoBuf.ProtoMember(1)]
        public Common Common { get; set; }

        /// <summary>
        /// 升级1 加入2
        /// </summary>
        [global::ProtoBuf.ProtoMember(2, Name = @"type")]
        public int Type { get; set; }

        [global::ProtoBuf.ProtoMember(3, Name = @"content")]
        [global::System.ComponentModel.DefaultValue("")]
        public string Content { get; set; } = "";

        [global::ProtoBuf.ProtoMember(4, Name = @"user")]
        public User User { get; set; }

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class Common : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        [global::ProtoBuf.ProtoMember(1, Name = @"method")]
        [global::System.ComponentModel.DefaultValue("")]
        public string Method { get; set; } = "";

        [global::ProtoBuf.ProtoMember(2)]
        public long msgId { get; set; }

        [global::ProtoBuf.ProtoMember(3)]
        public long roomId { get; set; }

        [global::ProtoBuf.ProtoMember(4)]
        public long createTime { get; set; }

        [global::ProtoBuf.ProtoMember(5, Name = @"monitor")]
        public int Monitor { get; set; }

        [global::ProtoBuf.ProtoMember(6)]
        public bool isShowMsg { get; set; }

        [global::ProtoBuf.ProtoMember(7, Name = @"describe")]
        [global::System.ComponentModel.DefaultValue("")]
        public string Describe { get; set; } = "";

        [global::ProtoBuf.ProtoMember(8)]
        public Text displayText { get; set; }

        [global::ProtoBuf.ProtoMember(9)]
        public long foldType { get; set; }

        [global::ProtoBuf.ProtoMember(10)]
        public long anchorFoldType { get; set; }

        [global::ProtoBuf.ProtoMember(11)]
        public long priorityScore { get; set; }

        [global::ProtoBuf.ProtoMember(12)]
        [global::System.ComponentModel.DefaultValue("")]
        public string logId { get; set; } = "";

        [global::ProtoBuf.ProtoMember(13)]
        [global::System.ComponentModel.DefaultValue("")]
        public string msgProcessFilterK { get; set; } = "";

        [global::ProtoBuf.ProtoMember(14)]
        [global::System.ComponentModel.DefaultValue("")]
        public string msgProcessFilterV { get; set; } = "";

        [global::ProtoBuf.ProtoMember(15, Name = @"user")]
        public User User { get; set; }

        [global::ProtoBuf.ProtoMember(16, Name = @"room")]
        public Room Room { get; set; }

        [global::ProtoBuf.ProtoMember(17)]
        public long anchorFoldTypeV2 { get; set; }

        [global::ProtoBuf.ProtoMember(18)]
        public long processAtSeiTimeMs { get; set; }

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class Text : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        [global::ProtoBuf.ProtoMember(1, Name = @"key")]
        [global::System.ComponentModel.DefaultValue("")]
        public string Key { get; set; } = "";

        [global::ProtoBuf.ProtoMember(2)]
        [global::System.ComponentModel.DefaultValue("")]
        public string defaultPattern { get; set; } = "";

        [global::ProtoBuf.ProtoMember(3)]
        public TextFormat defaultFormat { get; set; }

        [global::ProtoBuf.ProtoMember(4, Name = @"pieces")]
        public global::System.Collections.Generic.List<TextPiece> Pieces { get; } = new global::System.Collections.Generic.List<TextPiece>();

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class Room : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        [global::ProtoBuf.ProtoMember(1, Name = @"id")]
        public long Id { get; set; }

        [global::ProtoBuf.ProtoMember(2)]
        [global::System.ComponentModel.DefaultValue("")]
        public string idStr { get; set; } = "";

        [global::ProtoBuf.ProtoMember(3, Name = @"status")]
        public long Status { get; set; }

        [global::ProtoBuf.ProtoMember(4)]
        public long ownerUserId { get; set; }

        [global::ProtoBuf.ProtoMember(5, Name = @"title")]
        [global::System.ComponentModel.DefaultValue("")]
        public string Title { get; set; } = "";

        [global::ProtoBuf.ProtoMember(6)]
        public long userCount { get; set; }

        [global::ProtoBuf.ProtoMember(7)]
        public long createTime { get; set; }

        [global::ProtoBuf.ProtoMember(8)]
        public long linkmicLayout { get; set; }

        [global::ProtoBuf.ProtoMember(9)]
        public long finishTime { get; set; }

        [global::ProtoBuf.ProtoMember(10, Name = @"extra")]
        public RoomExtra Extra { get; set; }

        [global::ProtoBuf.ProtoMember(11)]
        [global::System.ComponentModel.DefaultValue("")]
        public string dynamicCoverUri { get; set; } = "";

        [global::ProtoBuf.ProtoMember(12, Name = @"dynamicCoverDict")]
        [global::ProtoBuf.ProtoMap]
        public global::System.Collections.Generic.Dictionary<long, string> dynamicCoverDicts { get; } = new global::System.Collections.Generic.Dictionary<long, string>();

        [global::ProtoBuf.ProtoMember(13)]
        public long lastPingTime { get; set; }

        [global::ProtoBuf.ProtoMember(14)]
        public long liveId { get; set; }

        [global::ProtoBuf.ProtoMember(15)]
        public long streamProvider { get; set; }

        [global::ProtoBuf.ProtoMember(16)]
        public long osType { get; set; }

        [global::ProtoBuf.ProtoMember(17)]
        public long clientVersion { get; set; }

        [global::ProtoBuf.ProtoMember(18)]
        public bool withLinkmic { get; set; }

        [global::ProtoBuf.ProtoMember(19)]
        public bool enableRoomPerspective { get; set; }

        [global::ProtoBuf.ProtoMember(20, Name = @"cover")]
        public Image Cover { get; set; }

        [global::ProtoBuf.ProtoMember(21)]
        public Image dynamicCover { get; set; }

        [global::ProtoBuf.ProtoMember(22)]
        public Image dynamicCoverLow { get; set; }

        [global::ProtoBuf.ProtoMember(23)]
        [global::System.ComponentModel.DefaultValue("")]
        public string shareUrl { get; set; } = "";

        [global::ProtoBuf.ProtoMember(24)]
        [global::System.ComponentModel.DefaultValue("")]
        public string anchorShareText { get; set; } = "";

        [global::ProtoBuf.ProtoMember(25)]
        [global::System.ComponentModel.DefaultValue("")]
        public string userShareText { get; set; } = "";

        [global::ProtoBuf.ProtoMember(26)]
        public long streamId { get; set; }

        [global::ProtoBuf.ProtoMember(27)]
        [global::System.ComponentModel.DefaultValue("")]
        public string streamIdStr { get; set; } = "";

        [global::ProtoBuf.ProtoMember(28)]
        public StreamUrl streamUrl { get; set; }

        [global::ProtoBuf.ProtoMember(29)]
        public long mosaicStatus { get; set; }

        [global::ProtoBuf.ProtoMember(30)]
        [global::System.ComponentModel.DefaultValue("")]
        public string mosaicTip { get; set; } = "";

        [global::ProtoBuf.ProtoMember(31)]
        public long cellStyle { get; set; }

        [global::ProtoBuf.ProtoMember(32)]
        public LinkMic linkMic { get; set; }

        [global::ProtoBuf.ProtoMember(33)]
        public long luckymoneyNum { get; set; }

        [global::ProtoBuf.ProtoMember(34, Name = @"decoList")]
        public global::System.Collections.Generic.List<Decoration> decoLists { get; } = new global::System.Collections.Generic.List<Decoration>();

        [global::ProtoBuf.ProtoMember(35)]
        public global::System.Collections.Generic.List<TopFan> topFans { get; } = new global::System.Collections.Generic.List<TopFan>();

        [global::ProtoBuf.ProtoMember(36, Name = @"stats")]
        public RoomStats Stats { get; set; }

        [global::ProtoBuf.ProtoMember(37)]
        [global::System.ComponentModel.DefaultValue("")]
        public string sunDailyIconContent { get; set; } = "";

        [global::ProtoBuf.ProtoMember(38, Name = @"distance")]
        [global::System.ComponentModel.DefaultValue("")]
        public string Distance { get; set; } = "";

        [global::ProtoBuf.ProtoMember(39)]
        [global::System.ComponentModel.DefaultValue("")]
        public string distanceCity { get; set; } = "";

        [global::ProtoBuf.ProtoMember(40, Name = @"location")]
        [global::System.ComponentModel.DefaultValue("")]
        public string Location { get; set; } = "";

        [global::ProtoBuf.ProtoMember(41)]
        [global::System.ComponentModel.DefaultValue("")]
        public string realDistance { get; set; } = "";

        [global::ProtoBuf.ProtoMember(42)]
        public Image feedRoomLabel { get; set; }

        [global::ProtoBuf.ProtoMember(43)]
        [global::System.ComponentModel.DefaultValue("")]
        public string commonLabelList { get; set; } = "";

        [global::ProtoBuf.ProtoMember(44)]
        public RoomUserAttr livingRoomAttrs { get; set; }

        [global::ProtoBuf.ProtoMember(45, IsPacked = true)]
        public long[] adminUserIds { get; set; }

        [global::ProtoBuf.ProtoMember(46, Name = @"owner")]
        public User Owner { get; set; }

        [global::ProtoBuf.ProtoMember(47)]
        [global::System.ComponentModel.DefaultValue("")]
        public string privateInfo { get; set; } = "";

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class RoomExtra : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class RoomStats : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class RoomUserAttr : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class StreamUrl : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class LinkMic : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class Decoration : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class TopFan : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class User : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        [global::ProtoBuf.ProtoMember(1, Name = @"id")]
        public long Id { get; set; }

        [global::ProtoBuf.ProtoMember(2)]
        public long shortId { get; set; }

        [global::ProtoBuf.ProtoMember(3, Name = @"nickname")]
        [global::System.ComponentModel.DefaultValue("")]
        public string Nickname { get; set; } = "";

        [global::ProtoBuf.ProtoMember(4, Name = @"gender")]
        public int Gender { get; set; }

        [global::ProtoBuf.ProtoMember(5, Name = @"signature")]
        [global::System.ComponentModel.DefaultValue("")]
        public string Signature { get; set; } = "";

        [global::ProtoBuf.ProtoMember(6, Name = @"level")]
        public int Level { get; set; }

        [global::ProtoBuf.ProtoMember(7, Name = @"birthday")]
        public long Birthday { get; set; }

        [global::ProtoBuf.ProtoMember(8, Name = @"telephone")]
        [global::System.ComponentModel.DefaultValue("")]
        public string Telephone { get; set; } = "";

        [global::ProtoBuf.ProtoMember(9)]
        public Image avatarThumb { get; set; }

        [global::ProtoBuf.ProtoMember(10)]
        public Image avatarMedium { get; set; }

        [global::ProtoBuf.ProtoMember(11)]
        public Image avatarLarge { get; set; }

        [global::ProtoBuf.ProtoMember(12, Name = @"verified")]
        public bool Verified { get; set; }

        [global::ProtoBuf.ProtoMember(13, Name = @"experience")]
        public int Experience { get; set; }

        [global::ProtoBuf.ProtoMember(14, Name = @"city")]
        [global::System.ComponentModel.DefaultValue("")]
        public string City { get; set; } = "";

        [global::ProtoBuf.ProtoMember(15, Name = @"status")]
        public int Status { get; set; }

        [global::ProtoBuf.ProtoMember(16)]
        public long createTime { get; set; }

        [global::ProtoBuf.ProtoMember(17)]
        public long modifyTime { get; set; }

        [global::ProtoBuf.ProtoMember(18, Name = @"secret")]
        public int Secret { get; set; }

        [global::ProtoBuf.ProtoMember(19)]
        [global::System.ComponentModel.DefaultValue("")]
        public string shareQrcodeUri { get; set; } = "";

        [global::ProtoBuf.ProtoMember(20)]
        public int incomeSharePercent { get; set; }

        [global::ProtoBuf.ProtoMember(21)]
        public Image badgeImageList { get; set; }

        [global::ProtoBuf.ProtoMember(22)]
        public FollowInfo followInfo { get; set; }

        [global::ProtoBuf.ProtoMember(23)]
        public PayGrade payGrade { get; set; }

        [global::ProtoBuf.ProtoMember(24)]
        public FansClub fansClub { get; set; }

        [global::ProtoBuf.ProtoMember(25)]
        public Border border { get; set; }

        [global::ProtoBuf.ProtoMember(26)]
        [global::System.ComponentModel.DefaultValue("")]
        public string specialId { get; set; } = "";

        [global::ProtoBuf.ProtoMember(27)]
        public Image avatarBorder { get; set; }

        [global::ProtoBuf.ProtoMember(28, Name = @"medal")]
        public Image Medal { get; set; }

        [global::ProtoBuf.ProtoMember(29)]
        public global::System.Collections.Generic.List<Image> realTimeIcons { get; } = new global::System.Collections.Generic.List<Image>();

        [global::ProtoBuf.ProtoMember(30)]
        public global::System.Collections.Generic.List<Image> newRealTimeIcons { get; } = new global::System.Collections.Generic.List<Image>();

        [global::ProtoBuf.ProtoMember(31)]
        public long topVipNo { get; set; }

        [global::ProtoBuf.ProtoMember(32)]
        public UserAttr userAttr { get; set; }

        [global::ProtoBuf.ProtoMember(33)]
        public OwnRoom ownRoom { get; set; }

        [global::ProtoBuf.ProtoMember(34)]
        public long payScore { get; set; }

        [global::ProtoBuf.ProtoMember(35)]
        public long ticketCount { get; set; }

        [global::ProtoBuf.ProtoMember(36)]
        public AnchorInfo anchorInfo { get; set; }

        [global::ProtoBuf.ProtoMember(37)]
        public int linkMicStats { get; set; }

        [global::ProtoBuf.ProtoMember(38)]
        [global::System.ComponentModel.DefaultValue("")]
        public string displayId { get; set; } = "";

        [global::ProtoBuf.ProtoMember(39)]
        [global::System.ComponentModel.DefaultValue("")]
        public string verified_content { get; set; } = "";

        [global::ProtoBuf.ProtoMember(46)]
        [global::System.ComponentModel.DefaultValue("")]
        public string sec_uid { get; set; } = "";

        [global::ProtoBuf.ProtoMember(47)]
        public int user_role { get; set; } = 0;

        [global::ProtoBuf.ProtoContract()]
        public partial class UserAttr : global::ProtoBuf.IExtensible
        {
            private global::ProtoBuf.IExtension __pbn__extensionData;
            global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
                => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        }

        [global::ProtoBuf.ProtoContract()]
        public partial class OwnRoom : global::ProtoBuf.IExtensible
        {
            private global::ProtoBuf.IExtension __pbn__extensionData;
            global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
                => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        }

        [global::ProtoBuf.ProtoContract()]
        public partial class AnchorInfo : global::ProtoBuf.IExtensible
        {
            private global::ProtoBuf.IExtension __pbn__extensionData;
            global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
                => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        }

        [global::ProtoBuf.ProtoContract()]
        public partial class FollowInfo : global::ProtoBuf.IExtensible
        {
            private global::ProtoBuf.IExtension __pbn__extensionData;
            global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
                => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

            [global::ProtoBuf.ProtoMember(1)]
            public long followingCount { get; set; }

            [global::ProtoBuf.ProtoMember(2)]
            public long followerCount { get; set; }

            [global::ProtoBuf.ProtoMember(3)]
            public long followStatus { get; set; }

            [global::ProtoBuf.ProtoMember(4)]
            public long pushStatus { get; set; }

            [global::ProtoBuf.ProtoMember(5)]
            [global::System.ComponentModel.DefaultValue("")]
            public string remarkName { get; set; } = "";

        }

        [global::ProtoBuf.ProtoContract()]
        public partial class FansClub : global::ProtoBuf.IExtensible
        {
            private global::ProtoBuf.IExtension __pbn__extensionData;
            global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
                => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

            [global::ProtoBuf.ProtoMember(1, Name = @"data")]
            public FansClubData Data { get; set; }

            [global::ProtoBuf.ProtoMember(2, Name = @"preferData")]
            [global::ProtoBuf.ProtoMap]
            public global::System.Collections.Generic.Dictionary<int, User.FansClub.FansClubData> preferDatas { get; } = new global::System.Collections.Generic.Dictionary<int, User.FansClub.FansClubData>();

            [global::ProtoBuf.ProtoContract()]
            public partial class FansClubData : global::ProtoBuf.IExtensible
            {
                private global::ProtoBuf.IExtension __pbn__extensionData;
                global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
                    => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

                [global::ProtoBuf.ProtoMember(1)]
                [global::System.ComponentModel.DefaultValue("")]
                public string clubName { get; set; } = "";

                [global::ProtoBuf.ProtoMember(2, Name = @"level")]
                public int Level { get; set; }

                [global::ProtoBuf.ProtoMember(3)]
                public int userFansClubStatus { get; set; }

                [global::ProtoBuf.ProtoMember(4, Name = @"badge")]
                public UserBadge Badge { get; set; }

                [global::ProtoBuf.ProtoMember(5, IsPacked = true)]
                public long[] availableGiftIds { get; set; }

                [global::ProtoBuf.ProtoMember(6)]
                public long anchorId { get; set; }

                [global::ProtoBuf.ProtoContract()]
                public partial class UserBadge : global::ProtoBuf.IExtensible
                {
                    private global::ProtoBuf.IExtension __pbn__extensionData;
                    global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
                        => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

                    [global::ProtoBuf.ProtoMember(1, Name = @"icons")]
                    [global::ProtoBuf.ProtoMap]
                    public global::System.Collections.Generic.Dictionary<int, Image> Icons { get; } = new global::System.Collections.Generic.Dictionary<int, Image>();

                    [global::ProtoBuf.ProtoMember(2, Name = @"title")]
                    [global::System.ComponentModel.DefaultValue("")]
                    public string Title { get; set; } = "";

                }

            }

        }

        [global::ProtoBuf.ProtoContract()]
        public partial class Border : global::ProtoBuf.IExtensible
        {
            private global::ProtoBuf.IExtension __pbn__extensionData;
            global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
                => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        }

        [global::ProtoBuf.ProtoContract()]
        public partial class GradeBuffInfo : global::ProtoBuf.IExtensible
        {
            private global::ProtoBuf.IExtension __pbn__extensionData;
            global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
                => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

            [global::ProtoBuf.ProtoMember(1)]
            public long buffLevel { get; set; }

            [global::ProtoBuf.ProtoMember(2, Name = @"status")]
            public int Status { get; set; }

            [global::ProtoBuf.ProtoMember(3)]
            public long endTime { get; set; }

            [global::ProtoBuf.ProtoMember(4, Name = @"statsInfo")]
            [global::ProtoBuf.ProtoMap]
            public global::System.Collections.Generic.Dictionary<long, long> statsInfoes { get; } = new global::System.Collections.Generic.Dictionary<long, long>();

            [global::ProtoBuf.ProtoMember(5)]
            public Image buffBadge { get; set; }

        }

        [global::ProtoBuf.ProtoContract()]
        public partial class PayGrade : global::ProtoBuf.IExtensible
        {
            private global::ProtoBuf.IExtension __pbn__extensionData;
            global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
                => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

            [global::ProtoBuf.ProtoMember(1)]
            public long totalDiamondCount { get; set; }

            [global::ProtoBuf.ProtoMember(2)]
            public Image diamondIcon { get; set; }

            [global::ProtoBuf.ProtoMember(3, Name = @"name")]
            [global::System.ComponentModel.DefaultValue("")]
            public string Name { get; set; } = "";

            [global::ProtoBuf.ProtoMember(4, Name = @"icon")]
            public Image Icon { get; set; }

            [global::ProtoBuf.ProtoMember(5)]
            [global::System.ComponentModel.DefaultValue("")]
            public string nextName { get; set; } = "";

            [global::ProtoBuf.ProtoMember(6, Name = @"level")]
            public long Level { get; set; }

            [global::ProtoBuf.ProtoMember(7)]
            public Image nextIcon { get; set; }

            [global::ProtoBuf.ProtoMember(8)]
            public long nextDiamond { get; set; }

            [global::ProtoBuf.ProtoMember(9)]
            public long nowDiamond { get; set; }

            [global::ProtoBuf.ProtoMember(10)]
            public long thisGradeMinDiamond { get; set; }

            [global::ProtoBuf.ProtoMember(11)]
            public long thisGradeMaxDiamond { get; set; }

            [global::ProtoBuf.ProtoMember(12)]
            public long payDiamondBak { get; set; }

            [global::ProtoBuf.ProtoMember(13)]
            [global::System.ComponentModel.DefaultValue("")]
            public string gradeDescribe { get; set; } = "";

            [global::ProtoBuf.ProtoMember(14, Name = @"gradeIconList")]
            public global::System.Collections.Generic.List<GradeIcon> gradeIconLists { get; } = new global::System.Collections.Generic.List<GradeIcon>();

            [global::ProtoBuf.ProtoMember(15)]
            public long screenChatType { get; set; }

            [global::ProtoBuf.ProtoMember(16)]
            public Image imIcon { get; set; }

            [global::ProtoBuf.ProtoMember(17)]
            public Image imIconWithLevel { get; set; }

            [global::ProtoBuf.ProtoMember(18)]
            public Image liveIcon { get; set; }

            [global::ProtoBuf.ProtoMember(19)]
            public Image newImIconWithLevel { get; set; }

            [global::ProtoBuf.ProtoMember(20)]
            public Image newLiveIcon { get; set; }

            [global::ProtoBuf.ProtoMember(21)]
            public long upgradeNeedConsume { get; set; }

            [global::ProtoBuf.ProtoMember(22)]
            [global::System.ComponentModel.DefaultValue("")]
            public string nextPrivileges { get; set; } = "";

            [global::ProtoBuf.ProtoMember(23, Name = @"background")]
            public Image Background { get; set; }

            [global::ProtoBuf.ProtoMember(24)]
            public Image backgroundBack { get; set; }

            [global::ProtoBuf.ProtoMember(25, Name = @"score")]
            public long Score { get; set; }

            [global::ProtoBuf.ProtoMember(26)]
            public User.GradeBuffInfo buffInfo { get; set; }

            [global::ProtoBuf.ProtoMember(1001)]
            [global::System.ComponentModel.DefaultValue("")]
            public string gradeBanner { get; set; } = "";

            [global::ProtoBuf.ProtoMember(1002)]
            public Image profileDialogBg { get; set; }

            [global::ProtoBuf.ProtoMember(1003)]
            public Image profileDialogBgBack { get; set; }

            [global::ProtoBuf.ProtoContract()]
            public partial class GradeIcon : global::ProtoBuf.IExtensible
            {
                private global::ProtoBuf.IExtension __pbn__extensionData;
                global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
                    => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

                [global::ProtoBuf.ProtoMember(1, Name = @"icon")]
                public Image Icon { get; set; }

                [global::ProtoBuf.ProtoMember(2)]
                public long iconDiamond { get; set; }

                [global::ProtoBuf.ProtoMember(3, Name = @"level")]
                public long Level { get; set; }

                [global::ProtoBuf.ProtoMember(4)]
                [global::System.ComponentModel.DefaultValue("")]
                public string levelStr { get; set; } = "";

            }

        }

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class TextFormat : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        [global::ProtoBuf.ProtoMember(1, Name = @"color")]
        [global::System.ComponentModel.DefaultValue("")]
        public string Color { get; set; } = "";

        [global::ProtoBuf.ProtoMember(2, Name = @"bold")]
        public bool Bold { get; set; }

        [global::ProtoBuf.ProtoMember(3, Name = @"italic")]
        public bool Italic { get; set; }

        [global::ProtoBuf.ProtoMember(4, Name = @"weight")]
        public int Weight { get; set; }

        [global::ProtoBuf.ProtoMember(5)]
        public int italicAngle { get; set; }

        [global::ProtoBuf.ProtoMember(6)]
        public int fontSize { get; set; }

        [global::ProtoBuf.ProtoMember(7)]
        public bool userHeightLightColor { get; set; }

        [global::ProtoBuf.ProtoMember(8)]
        public bool useRemoteClor { get; set; }

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class TextPiece : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        [global::ProtoBuf.ProtoMember(1, Name = @"type")]
        public int Type { get; set; }

        [global::ProtoBuf.ProtoMember(2, Name = @"format")]
        public TextFormat Format { get; set; }

        [global::ProtoBuf.ProtoMember(11)]
        [global::System.ComponentModel.DefaultValue("")]
        public string stringValue { get; set; } = "";

        [global::ProtoBuf.ProtoMember(21)]
        public TextPieceUser userValue { get; set; }

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class Image : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        [global::ProtoBuf.ProtoMember(1, Name = @"urlList")]
        public global::System.Collections.Generic.List<string> urlLists { get; } = new global::System.Collections.Generic.List<string>();

        [global::ProtoBuf.ProtoMember(2, Name = @"uri")]
        [global::System.ComponentModel.DefaultValue("")]
        public string Uri { get; set; } = "";

        [global::ProtoBuf.ProtoMember(3, Name = @"height")]
        public long Height { get; set; }

        [global::ProtoBuf.ProtoMember(4, Name = @"width")]
        public long Width { get; set; }

        [global::ProtoBuf.ProtoMember(5)]
        [global::System.ComponentModel.DefaultValue("")]
        public string avgColor { get; set; } = "";

        [global::ProtoBuf.ProtoMember(6)]
        public int imageType { get; set; }

        [global::ProtoBuf.ProtoMember(7)]
        [global::System.ComponentModel.DefaultValue("")]
        public string openWebUrl { get; set; } = "";

        [global::ProtoBuf.ProtoMember(8)]
        public Content content { get; set; }

        [global::ProtoBuf.ProtoMember(9)]
        public bool isAnimated { get; set; }

        [global::ProtoBuf.ProtoContract()]
        public partial class Content : global::ProtoBuf.IExtensible
        {
            private global::ProtoBuf.IExtension __pbn__extensionData;
            global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
                => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

            [global::ProtoBuf.ProtoMember(1, Name = @"name")]
            [global::System.ComponentModel.DefaultValue("")]
            public string Name { get; set; } = "";

            [global::ProtoBuf.ProtoMember(2)]
            [global::System.ComponentModel.DefaultValue("")]
            public string fontColor { get; set; } = "";

            [global::ProtoBuf.ProtoMember(3, Name = @"level")]
            public long Level { get; set; }

            [global::ProtoBuf.ProtoMember(4)]
            [global::System.ComponentModel.DefaultValue("")]
            public string alternativeText { get; set; } = "";

        }

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class TextPieceUser : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        [global::ProtoBuf.ProtoMember(1, Name = @"user")]
        public User User { get; set; }

        [global::ProtoBuf.ProtoMember(2)]
        public bool withColon { get; set; }

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class PublicAreaCommon : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        [global::ProtoBuf.ProtoMember(1)]
        public Image userLabel { get; set; }

        [global::ProtoBuf.ProtoMember(2)]
        public long userConsumeInRoom { get; set; }

        [global::ProtoBuf.ProtoMember(3)]
        public long userSendGiftCntInRoom { get; set; }

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class GiftIMPriority : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        [global::ProtoBuf.ProtoMember(1, IsPacked = true)]
        public long[] queueSizes { get; set; }

        [global::ProtoBuf.ProtoMember(2)]
        public long selfQueuePriority { get; set; }

        [global::ProtoBuf.ProtoMember(3, Name = @"priority")]
        public long Priority { get; set; }

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class GiftTrayInfo : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        [global::ProtoBuf.ProtoMember(1)]
        public Text trayDisplayText { get; set; }

        [global::ProtoBuf.ProtoMember(2)]
        public Image trayBaseImg { get; set; }

        [global::ProtoBuf.ProtoMember(3)]
        public Image trayHeadImg { get; set; }

        [global::ProtoBuf.ProtoMember(4)]
        public Image trayRightImg { get; set; }

        [global::ProtoBuf.ProtoMember(5)]
        public long trayLevel { get; set; }

        [global::ProtoBuf.ProtoMember(6)]
        public Image trayDynamicImg { get; set; }

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class GiftStruct : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        [global::ProtoBuf.ProtoMember(1, Name = @"image")]
        public Image Image { get; set; }

        [global::ProtoBuf.ProtoMember(2, Name = @"describe")]
        [global::System.ComponentModel.DefaultValue("")]
        public string Describe { get; set; } = "";

        [global::ProtoBuf.ProtoMember(3, Name = @"notify")]
        public bool Notify { get; set; }

        [global::ProtoBuf.ProtoMember(4, Name = @"duration")]
        public long Duration { get; set; }

        [global::ProtoBuf.ProtoMember(5, Name = @"id")]
        public long Id { get; set; }

        [global::ProtoBuf.ProtoMember(6)]
        public GiftStructFansClubInfo fansclubInfo { get; set; }

        [global::ProtoBuf.ProtoMember(7)]
        public bool forLinkmic { get; set; }

        [global::ProtoBuf.ProtoMember(8, Name = @"doodle")]
        public bool Doodle { get; set; }

        [global::ProtoBuf.ProtoMember(9)]
        public bool forFansclub { get; set; }

        [global::ProtoBuf.ProtoMember(10, Name = @"combo")]
        public bool Combo { get; set; }

        [global::ProtoBuf.ProtoMember(11, Name = @"type")]
        public int Type { get; set; }

        [global::ProtoBuf.ProtoMember(12)]
        public int diamondCount { get; set; }

        [global::ProtoBuf.ProtoMember(13)]
        public int isDisplayedOnPanel { get; set; }

        [global::ProtoBuf.ProtoMember(14)]
        public long primaryEffectId { get; set; }

        [global::ProtoBuf.ProtoMember(15)]
        public Image giftLabelIcon { get; set; }

        [global::ProtoBuf.ProtoMember(16, Name = @"name")]
        [global::System.ComponentModel.DefaultValue("")]
        public string Name { get; set; } = "";

        [global::ProtoBuf.ProtoMember(17, Name = @"region")]
        [global::System.ComponentModel.DefaultValue("")]
        public string Region { get; set; } = "";

        [global::ProtoBuf.ProtoMember(18, Name = @"manual")]
        [global::System.ComponentModel.DefaultValue("")]
        public string Manual { get; set; } = "";

        [global::ProtoBuf.ProtoMember(19)]
        public bool forCustom { get; set; }

        [global::ProtoBuf.ProtoMember(20)]
        [global::ProtoBuf.ProtoMap]
        public global::System.Collections.Generic.Dictionary<string, long> specialEffects { get; } = new global::System.Collections.Generic.Dictionary<string, long>();

        [global::ProtoBuf.ProtoMember(21, Name = @"icon")]
        public Image Icon { get; set; }

        [global::ProtoBuf.ProtoMember(22)]
        public int actionType { get; set; }

        [global::ProtoBuf.ProtoMember(23)]
        public int watermelonSeeds { get; set; }

        [global::ProtoBuf.ProtoMember(24)]
        [global::System.ComponentModel.DefaultValue("")]
        public string goldEffect { get; set; } = "";

        [global::ProtoBuf.ProtoMember(25, Name = @"subs")]
        public global::System.Collections.Generic.List<LuckyMoneyGiftMeta> Subs { get; } = new global::System.Collections.Generic.List<LuckyMoneyGiftMeta>();

        [global::ProtoBuf.ProtoMember(26)]
        public long goldenBeans { get; set; }

        [global::ProtoBuf.ProtoMember(27)]
        public long honorLevel { get; set; }

        [global::ProtoBuf.ProtoMember(28)]
        public int itemType { get; set; }

        [global::ProtoBuf.ProtoMember(29)]
        [global::System.ComponentModel.DefaultValue("")]
        public string schemeUrl { get; set; } = "";

        [global::ProtoBuf.ProtoMember(30)]
        public GiftPanelOperation giftOperation { get; set; }

        [global::ProtoBuf.ProtoMember(31)]
        [global::System.ComponentModel.DefaultValue("")]
        public string eventName { get; set; } = "";

        [global::ProtoBuf.ProtoMember(32)]
        public long nobleLevel { get; set; }

        [global::ProtoBuf.ProtoMember(33)]
        [global::System.ComponentModel.DefaultValue("")]
        public string guideUrl { get; set; } = "";

        [global::ProtoBuf.ProtoMember(34)]
        public bool punishMedicine { get; set; }

        [global::ProtoBuf.ProtoMember(35)]
        public bool forPortal { get; set; }

        [global::ProtoBuf.ProtoMember(36)]
        [global::System.ComponentModel.DefaultValue("")]
        public string businessText { get; set; } = "";

        [global::ProtoBuf.ProtoMember(37)]
        public bool cnyGift { get; set; }

        [global::ProtoBuf.ProtoMember(38)]
        public long appId { get; set; }

        [global::ProtoBuf.ProtoMember(39)]
        public long vipLevel { get; set; }

        [global::ProtoBuf.ProtoMember(40)]
        public bool isGray { get; set; }

        [global::ProtoBuf.ProtoMember(41)]
        [global::System.ComponentModel.DefaultValue("")]
        public string graySchemeUrl { get; set; } = "";

        [global::ProtoBuf.ProtoMember(42)]
        public long giftScene { get; set; }

        [global::ProtoBuf.ProtoMember(43)]
        public GiftBanner giftBanner { get; set; }

        [global::ProtoBuf.ProtoMember(44)]
        public global::System.Collections.Generic.List<string> triggerWords { get; } = new global::System.Collections.Generic.List<string>();

        [global::ProtoBuf.ProtoMember(45)]
        public global::System.Collections.Generic.List<GiftBuffInfo> giftBuffInfos { get; } = new global::System.Collections.Generic.List<GiftBuffInfo>();

        [global::ProtoBuf.ProtoMember(46)]
        public bool forFirstRecharge { get; set; }

        [global::ProtoBuf.ProtoMember(47)]
        public Image dynamicImgForSelected { get; set; }

        [global::ProtoBuf.ProtoMember(48)]
        public int afterSendAction { get; set; }

        [global::ProtoBuf.ProtoMember(49)]
        public long giftOfflineTime { get; set; }

        [global::ProtoBuf.ProtoMember(50)]
        [global::System.ComponentModel.DefaultValue("")]
        public string topBarText { get; set; } = "";

        [global::ProtoBuf.ProtoMember(51)]
        public Image topRightAvatar { get; set; }

        [global::ProtoBuf.ProtoMember(52)]
        [global::System.ComponentModel.DefaultValue("")]
        public string bannerSchemeUrl { get; set; } = "";

        [global::ProtoBuf.ProtoMember(53)]
        public bool isLocked { get; set; }

        [global::ProtoBuf.ProtoMember(54)]
        public long reqExtraType { get; set; }

        [global::ProtoBuf.ProtoMember(55, IsPacked = true)]
        public long[] assetIds { get; set; }

        [global::ProtoBuf.ProtoMember(56)]
        public GiftPreviewInfo giftPreviewInfo { get; set; }

        [global::ProtoBuf.ProtoMember(57)]
        public GiftTip giftTip { get; set; }

        [global::ProtoBuf.ProtoMember(58)]
        public int needSweepLightCount { get; set; }

        [global::ProtoBuf.ProtoMember(59, Name = @"groupInfo")]
        public global::System.Collections.Generic.List<GiftGroupInfo> groupInfoes { get; } = new global::System.Collections.Generic.List<GiftGroupInfo>();

        [global::ProtoBuf.ProtoContract()]
        public partial class GiftStructFansClubInfo : global::ProtoBuf.IExtensible
        {
            private global::ProtoBuf.IExtension __pbn__extensionData;
            global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
                => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

            [global::ProtoBuf.ProtoMember(1)]
            public int minLevel { get; set; }

            [global::ProtoBuf.ProtoMember(2)]
            public int insertPos { get; set; }

        }

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class AssetEffectMixInfo : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class LuckyMoneyGiftMeta : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class GiftPanelOperation : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class GiftBanner : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class GiftBuffInfo : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class GiftPreviewInfo : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class GiftTip : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class GiftGroupInfo : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class EffectMixImageInfo : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

    }

#pragma warning restore CS0612, CS0618, CS1591, CS3021, IDE0079, IDE1006, RCS1036, RCS1057, RCS1085, RCS1192

}

