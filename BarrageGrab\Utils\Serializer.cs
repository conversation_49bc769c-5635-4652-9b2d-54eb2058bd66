﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;

namespace BarrageGrab.Utils
{
    internal class Serializer
    {
        private byte[] StringToByte(string str)
        {
            return Encoding.UTF8.GetBytes(str);
        }
        private string EscapeString(string str)
        {
            return str.Replace("@", "@A").Replace("/", "@S");
        }

        public string Serialize(object obj)
        {
            if (obj is JObject)
            {
                return SerializeDictionary(((JObject)obj).ToObject<Dictionary<string, object>>());
            }
            else if (obj is JArray)
            {
                return SerializeList(((JArray)obj).ToObject<List<object>>());
            }
            else
            {
                return EscapeString(obj.ToString());
            }
        }

        private string SerializeDictionary(IDictionary dict)
        {
            var sb = new StringBuilder();
            foreach (DictionaryEntry entry in dict)
            {
                string key = EscapeString(entry.Key.ToString());
                string value = Serialize(entry.Value);
                sb.Append(key).Append("@=").Append(value).Append("/");
            }
            return sb.ToString();
        }

        private string SerializeList(IList list)
        {
            var sb = new StringBuilder();
            foreach (var item in list)
            {
                sb.Append(Serialize(item)).Append("/");
            }
            return sb.ToString();
        }


        public byte[] STTSerialize(string json)
        {
            var raw = JsonConvert.DeserializeObject(json);
            var bytes = Serialize(raw);
            return StringToByte(bytes);
        }

    }

    internal class Deserializer
    {
        private string ByteToString(byte[] bytes)
        {
            return Encoding.UTF8.GetString(bytes);
        }

        public object Deserialize(string serialized)
        {
            if (serialized.EndsWith("/"))
            {
                string[] parts = serialized.TrimEnd('/').Split(new[] { '/' }, StringSplitOptions.None);
                List<object> list = new List<object>();
                foreach (var part in parts)
                {
                    if (part.Contains("@="))
                    {
                        list.Add(DeserializeDictionary(part));
                    }
                    else
                    {
                        list.Add(UnescapeString(part));
                    }
                }
                return list;
            }
            else
            {
                return DeserializeDictionary(serialized);
            }
        }

        private Dictionary<string, object> DeserializeDictionary(string serialized)
        {
            Dictionary<string, object> dict = new Dictionary<string, object>();
            string[] keyValuePairs = serialized.Split(new[] { '/' }, StringSplitOptions.RemoveEmptyEntries);
            foreach (string keyValuePair in keyValuePairs)
            {
                string[] keyValue = keyValuePair.Split(new[] { "@=" }, StringSplitOptions.None);
                if (keyValue.Length != 2)
                {
                    throw new ArgumentException("Invalid input string.");
                }

                string key = UnescapeString(keyValue[0]);
                object value = Deserialize(keyValue[1]);
                dict[key] = value;
            }

            return dict;
        }

        private string UnescapeString(string input)
        {
            return input.Replace("@S", "/").Replace("@A", "@");
        }

        public object STTDeserialize(byte[] bytes)
        {
            var raw = ByteToString(bytes);
            return Deserialize(raw);
        }
    }
}
