﻿using System;
using System.Diagnostics;

namespace BarrageGrab.Utils
{
    internal class ExecuteLocal
    {
        public static void RunBatchFile(string filePath)
        {
            var startInfo = new ProcessStartInfo
            {
                FileName = "cmd.exe",
                Arguments = $"/c \"{filePath}\"",
                RedirectStandardOutput = true,
                RedirectStandardError = true, // Capture standard error output
                UseShellExecute = false
            };

            using (var process = new Process { StartInfo = startInfo })
            {
                process.Start();

                // Read standard output and error
                string output = process.StandardOutput.ReadToEnd();
                string error = process.StandardError.ReadToEnd();

                process.WaitForExit();

                // Throw an exception if the exit code is non-zero
                if (process.ExitCode != 0)
                {
                    throw new Exception($"Error executing batch file: {error}");
                }
            }
        }
    }
}
