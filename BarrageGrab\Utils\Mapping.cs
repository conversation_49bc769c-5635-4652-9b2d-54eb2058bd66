﻿using System;

namespace BarrageGrab.Utils
{
    public class ChatLog
    {
        public int Id { get; set; }
        public string Sender { get; set; }
        public string SenderId { get; set; }
        public string Content { get; set; }
        public DateTime TimeStamp { get; set; }
    }

    public class GiftLog
    {
        public int Id { get; set; }
        public string Sender { get; set; }
        public string SenderId { get; set; }
        public string Receiver { get; set; }
        public string GiftName { get; set; }
        public int Value { get; set; }
        public int Count { get; set; }
        public DateTime TimeStamp { get; set; }
    }

    public class SysEventLog
    {
        public int Id { get; set; }
        public string EventType { get; set; }
        public string EventContent { get; set; }
        public DateTime TimeStamp { get; set; }
    }

    public class User
    {
        public int Id { get; set; }
        public string KnownAs { get; set; }
        public bool? IsOnline { get; set; }
        public bool IsDisabled { get; set; }
        public string AuthCode { get; set; }
        public string Config { get; set; }
    }
}
