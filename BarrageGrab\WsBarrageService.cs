using BarrageGrab.JsonEntity;
using BarrageGrab.ProtoEntity;
using BarrageGrab.Utils;
using ColorConsole;
using Fleck;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Timers;
using Timer = System.Timers.Timer;

namespace BarrageGrab
{

    /// <summary>
    /// 弹幕服务
    /// </summary>
    internal class WsBarrageService
    {

        WebSocketServer socketServer;
        Dictionary<string, UserState> socketList = new Dictionary<string, UserState>();
        //礼物计数缓存
        Dictionary<string, Tuple<int, DateTime>> giftCountCache = new Dictionary<string, Tuple<int, DateTime>>();
        Timer dieout = new Timer(10000);
        Timer giftCountTimer = new Timer(10000);

        private object timerLock = new object();
        Timer wsIdleTimer = new Timer(Appsetting.Current.IdleTimeOut * 1000);
        int timeoutCountOutofRange = 0;

        Timer serviceResetTimer;

        ConsoleWriter console = new ConsoleWriter();
        WssBarrageGrab grab = new WssBarrageGrab();
        Appsetting Appsetting = Appsetting.Current;
        Serializer stt = new Serializer();

        static string connStr = Appsetting.Current.DbConnStr;
        Logger logger = new Logger(connStr);
        
        public WsBarrageService()
        {
            var socket = new WebSocketServer($"ws://0.0.0.0:{Appsetting.WsProt}");
            socket.RestartAfterListenError = true;//异常重启

            dieout.Elapsed += Dieout_Elapsed;
            giftCountTimer.Elapsed += GiftCountTimer_Elapsed;
            wsIdleTimer.Elapsed += WsIdleTimer_Elapsed;

            this.grab.OnChatMessage += Grab_OnChatMessage;
            this.grab.OnLikeMessage += Grab_OnLikeMessage;
            this.grab.OnMemberMessage += Grab_OnMemberMessage;
            this.grab.OnSocialMessage += Grab_OnSocialMessage;
            this.grab.OnSocialMessage += Grab_OnShardMessage;
            this.grab.OnGiftMessage += Grab_OnGiftMessage;
            this.grab.OnRoomUserSeqMessage += Grab_OnRoomUserSeqMessage;
            this.grab.OnFansclubMessage += Grab_OnFansclubMessage; ;
            this.grab.OnControlMessage += Grab_OnControlMessage;

            this.socketServer = socket;
            //dieout.Start();
            wsIdleTimer.AutoReset = false;
            wsIdleTimer.Start();
            giftCountTimer.Start();
        }

        private DateTime CalculateNextResetTime(int startHour)
        {
            DateTime now = DateTime.Now;
            DateTime nextRun = new DateTime(now.Year, now.Month, now.Day, startHour, 0, 0);
            if (nextRun < now || nextRun == now)
                nextRun = nextRun.AddDays(1);

            return nextRun;
        }

        private void SetServiceRestartTimer()
        {
            // calculate next time interval
            var nextRun = CalculateNextResetTime(Appsetting.ActiveHourStart);
            ReCreateTimer(ref serviceResetTimer, nextRun.Subtract(DateTime.Now).TotalSeconds, ServiceResetTimer_Elapsed);
        }

        private void ReCreateTimer(ref Timer timer, double interval, ElapsedEventHandler elapsedEventHandler)
        {
            if (timer != null)
            {
                StopTimer(timer);
                timer.Dispose();
            }
            timer = new Timer(interval * 1000) { AutoReset = false };
            timer.Elapsed += elapsedEventHandler;
            StartTimer(timer);
        }

        private void ServiceResetTimer_Elapsed(object sender, ElapsedEventArgs e)
        {
            var logMessage = "活跃时间段开始, 重设计时器";
            logger.LogToConsole(logMessage, ConsoleColor.Magenta);
            logger.LogToFile(logMessage, Logger.LogIndex.程序日志);
            // Do work
            ExecuteReset();
            ReCreateTimer(ref wsIdleTimer, Appsetting.IdleTimeOut, WsIdleTimer_Elapsed);
        }



        private void StopTimer(Timer timer)
        {
            lock (timerLock)
            {
                timer.Stop();
            }
        }

        private void StartTimer(Timer timer)
        {
            lock (timerLock)
            {
                timer.Start();
            }
        }

        private void ResetTimer(Timer timer)
        {
            lock (timerLock)
            {
                if (timer.Enabled)
                {
                    StopTimer(timer);
                    StartTimer(timer);
                }
                else
                {
                    StartTimer(timer);
                }
            }
        }

        private bool IsCurrentHourInRange()
        {
            var startHour = Appsetting.ActiveHourStart;
            var endHour = Appsetting.ActiveHourEnd;
            int currentHour = DateTime.Now.Hour;

            if (!CheckStartHourValid(startHour, endHour)) return true;

            if (startHour <= endHour)
            {
                // Normal range, not crossing midnight
                return currentHour >= startHour && currentHour <= endHour;
            }
            else
            {
                // Range crosses midnight
                return currentHour >= startHour || currentHour <= endHour;
            }
        }

        private bool CheckStartHourValid(int startHour, int endHour)
        {
            if (startHour == endHour && startHour == 0) return false; return true;
        }


        private void WsIdleTimer_Elapsed(object sender, ElapsedEventArgs e)
        {
            var batchReset = Appsetting.WebResetScriptPath;

            var logMessage = $"消息接收超时, 当前检测间隔: {wsIdleTimer.Interval / 1000}s";
            logger.LogToConsole(logMessage, ConsoleColor.Magenta);
            logger.LogToFile(logMessage, Logger.LogIndex.程序日志);

            ExecuteReset();

            if(!IsCurrentHourInRange())
            {
                timeoutCountOutofRange += 1;
                if (timeoutCountOutofRange > 5)
                {
                    var logMessageAssumeStreamClosed = "消息接收超时五次, 假设直播已结束, 关闭计时器";
                    logger.LogToConsole(logMessageAssumeStreamClosed, ConsoleColor.Magenta);
                    logger.LogToFile(logMessageAssumeStreamClosed, Logger.LogIndex.程序日志);
                    timeoutCountOutofRange = 0;
                    SetServiceRestartTimer();
                    return;
                }
            }

            StartTimer(wsIdleTimer);

        }
        
        void ExecuteReset()
        {
            var batchReset = Appsetting.WebResetScriptPath;
            if (string.IsNullOrEmpty(batchReset))
            {
                var logMessageNoScript = "无批处理配置, 关闭计时器";
                logger.LogToConsole(logMessageNoScript, ConsoleColor.Magenta);
                logger.LogToFile(logMessageNoScript, Logger.LogIndex.程序日志);
                return;
            }

            try
            {
                var logMessageGoForReset = "尝试重置Web端";
                logger.LogToConsole(logMessageGoForReset, ConsoleColor.Magenta);
                logger.LogToFile(logMessageGoForReset, Logger.LogIndex.程序日志);
                ExecuteLocal.RunBatchFile(batchReset);
            }
            catch (Exception ex)
            {
                var logMessageErr = $"执行错误: {ex.Message}, 关闭计时器";
                logger.LogToConsole(logMessageErr, ConsoleColor.Magenta);
                logger.LogToFile(logMessageErr, Logger.LogIndex.程序日志);
                return;
            }
        }

        private void GiftCountTimer_Elapsed(object sender, ElapsedEventArgs e)
        {
            var now = DateTime.Now;
            var timeOutKeys = giftCountCache.Where(w => w.Value.Item2 < now.AddSeconds(-10) || w.Value == null).Select(s => s.Key).ToList();

            //淘汰过期的礼物计数缓存
            lock (giftCountCache)
            {
                timeOutKeys.ForEach(key =>
                {
                    giftCountCache.Remove(key);
                });
            }
        }

        private bool CheckRoomId(long roomid)
        {
            return Appsetting.RoomIds.Length == 0 || Appsetting.RoomIds.Contains(roomid);
        }

        //解析用户
        private MsgUser GetUser(ProtoEntity.User data)
        {
            MsgUser user = new MsgUser()
            {
                DisplayId = data.displayId,
                Gender = data.Gender,
                Id = data.Id,
                Level = data.Level,
                Nickname = data.Nickname,
                HeadImgUrl = data.avatarThumb?.urlLists.FirstOrDefault() ?? "",
                SecUid = data.sec_uid
            };
            user.FansClub = new FansClubInfo()
            {
                ClubName = "",
                Level = 0
            };

            if (data.fansClub != null && data.fansClub.Data != null)
            {
                user.FansClub.ClubName = data.fansClub.Data.clubName;
                user.Level = data.fansClub.Data.Level;
            }

            return user;
        }

        //粉丝团
        private void Grab_OnFansclubMessage(object sender, ProtoEntity.FansclubMessage e)
        {
            //if (!CheckRoomId(e.Common.roomId)) return;
            //var enty = new FansclubMsg()
            //{
            //    MsgId = e.Common.msgId,
            //    Content = e.Content,
            //    RoomId = e.Common.roomId,
            //    Type = e.Type,
            //    User = GetUser(e.User)
            //};
            //Print(enty.User.GenderToString() + "  " + enty.Content, ConsoleColor.Blue, BarrageMsgType.粉丝团消息);
            //var pack = new BarrageMsgPack(JsonConvert.SerializeObject(enty), BarrageMsgType.粉丝团消息);
            //var json = JsonConvert.SerializeObject(pack);
            //this.Broadcast(json);
        }

        //统计消息
        private void Grab_OnRoomUserSeqMessage(object sender, ProtoEntity.RoomUserSeqMessage e)
        {
            //if (!CheckRoomId(e.Common.roomId)) return;
            //var enty = new UserSeqMsg()
            //{
            //    MsgId = e.Common.msgId,
            //    OnlineUserCount = e.Total,
            //    TotalUserCount = e.totalUser,
            //    TotalUserCountStr = e.totalPvForAnchor,
            //    OnlineUserCountStr = e.onlineUserForAnchor,
            //    RoomId = e.Common.roomId,
            //    Content = $"当前直播间人数 {e.onlineUserForAnchor}，累计直播间人数 {e.totalPvForAnchor}",
            //    User = null
            //};
            //Print(enty.Content, ConsoleColor.Magenta, BarrageMsgType.直播间统计);
            //var pack = new BarrageMsgPack(JsonConvert.SerializeObject(enty), BarrageMsgType.直播间统计);
            //var json = JsonConvert.SerializeObject(pack);
            //this.Broadcast(json);
        }

        //礼物
        private void Grab_OnGiftMessage(object sender, ProtoEntity.GiftMessage e)
        {

            if (!CheckRoomId(e.Common.roomId)) return;
            timeoutCountOutofRange = 0;
            ResetTimer(wsIdleTimer);

            var key = e.giftId + "-" + e.groupId.ToString() + "-" + e.User.Nickname + "-" + e.toUser.Nickname ?? "N/A";

            //判断礼物重复
            if (e.repeatEnd == 1)
            {
                //清除缓存中的key
                if (e.groupId > 0 && giftCountCache.ContainsKey(key))
                {
                    lock (giftCountCache)
                    {
                        giftCountCache.Remove(key);
                    }
                }
                return;
            }

            int lastCount = 0;
            int currCount = (int)e.repeatCount;
            var backward = currCount <= lastCount;
            if (currCount <= 0) currCount = 1;

            if (giftCountCache.ContainsKey(key))
            {
                lastCount = giftCountCache[key].Item1;
                backward = currCount <= lastCount;
                if (!backward)
                {
                    lock (giftCountCache)
                    {
                        giftCountCache[key] = Tuple.Create(currCount, DateTime.Now);
                    }
                }
            }
            else
            {
                if (e.groupId > 0 && !backward)
                {
                    lock (giftCountCache)
                    {
                        giftCountCache.Add(key, Tuple.Create(currCount, DateTime.Now));
                    }
                }
            }
            //比上次小，则说明先后顺序出了问题，直接丢掉，应为比它大的消息已经处理过了
            if (backward) return;


            var singleCount = currCount - lastCount;

            //var enty = new GiftMsg()
            //{
            //    MsgId = e.Common.msgId,
            //    RoomId = e.Common.roomId,
            //    //Content = $"{e.User.Nickname} : {e.Gift.Name} x {currCount} -> {e.toUser.Nickname}，count -\ngroup: {e.groupCount} |  repeat: {e.repeatCount} | combo: {e.comboCount} ",//增量:{count}",
            //    DiamondCount = e.Gift.diamondCount,
            //    RepeatCount = currCount,
            //    GiftCount = count,
            //    GiftId = e.giftId,
            //    GiftName = e.Gift.Name,
            //    User = GetUser(e.User),
            //    //To = GetUser(e.toUser),
            //};

            var newEntity = new STTGift()
            {
                Type = "dgb",
                Key = e.Common.msgId,
                UserName = e.User.Nickname,
                UserDisplayId = e.User.displayId,
                UserSecUid = e.User.sec_uid,
                UserLevel = e.User.payGrade.Level,
                GiftId = e.Gift.Id,
                GiftName = e.Gift.Name,
                Count = singleCount,
                Hits = (int)e.comboCount,
                BatchId = e.groupId,
                Price = e.Gift.diamondCount,
                To = e.toUser?.Nickname ?? "N/A"
            };

            var dbRecord = new GiftLog()
            {
                Sender = e.User.Nickname,
                SenderId = e.User.displayId,
                Receiver = e.toUser.Nickname,
                GiftName = e.Gift.Name,
                Value = e.Gift.diamondCount,
                Count = singleCount,
                TimeStamp = DateTime.Now,
            };

            var logMessage = $"{e.User.Nickname}({e.User.displayId}) : {e.Gift.Name}({e.Gift.diamondCount}) x {singleCount} -> {e.toUser?.Nickname}，数量 - 捆绑: {e.groupCount} |  累计: {e.repeatCount} | 连击: {e.comboCount}";//增量:{count}",

            //Print(logMessage, ConsoleColor.Red, BarrageMsgType.礼物消息);
            //var pack = new BarrageMsgPack(JsonConvert.SerializeObject(enty), BarrageMsgType.礼物消息);
            //var json = JsonConvert.SerializeObject(pack);
            //this.Broadcast(json);
            var json = JsonConvert.SerializeObject(newEntity);
            var stream = stt.STTSerialize(json);
            this.Broadcast(stream, e.Common.roomId);
            logger.LogToFile(logMessage, Logger.LogIndex.礼物, e.Common.roomId);
            logger.LogGiftToDb(dbRecord);
        }

        //关注
        private void Grab_OnSocialMessage(object sender, ProtoEntity.SocialMessage e)
        {
            //if (!CheckRoomId(e.Common.roomId)) return;
            //if (e.Action != 1) return;
            //var enty = new Msg()
            //{
            //    MsgId = e.Common.msgId,
            //    Content = $"{e.User.Nickname} 关注了主播",
            //    RoomId = e.Common.roomId,
            //    User = GetUser(e.User)
            //};

            //Print($"{enty.User.GenderToString()}  {enty.Content}", ConsoleColor.Yellow, BarrageMsgType.关注消息);
            //var pack = new BarrageMsgPack(JsonConvert.SerializeObject(enty), BarrageMsgType.关注消息);
            //var json = JsonConvert.SerializeObject(pack);
            //this.Broadcast(json);
        }

        //直播间分享
        private void Grab_OnShardMessage(object sender, ProtoEntity.SocialMessage e)
        {
            //if (!CheckRoomId(e.Common.roomId)) return;
            //if (e.Action != 3) return;
            //ShareType type = ShareType.未知;
            //if (Enum.IsDefined(type.GetType(), int.Parse(e.shareTarget)))
            //{
            //    type = (ShareType)int.Parse(e.shareTarget);
            //}

            //var enty = new ShareMessage()
            //{
            //    MsgId = e.Common.msgId,
            //    Content = $"{e.User.Nickname} 分享了直播间到{type}",
            //    RoomId = e.Common.roomId,
            //    ShareType = type,
            //    User = GetUser(e.User)
            //};
            //shareTarget: (112:好友),(1微信)(2朋友圈)(3微博)(5:qq)(4:qq空间),shareType: 1
            //Print($"{enty.User.GenderToString()}  {enty.Content}", ConsoleColor.DarkBlue, BarrageMsgType.直播间分享);
            //var pack = new BarrageMsgPack(JsonConvert.SerializeObject(enty), BarrageMsgType.直播间分享);
            //var json = JsonConvert.SerializeObject(pack);
            //this.Broadcast(json);
        }

        //来了
        private void Grab_OnMemberMessage(object sender, ProtoEntity.MemberMessage e)
        {

            if (!CheckRoomId(e.Common.roomId)) return;
            timeoutCountOutofRange = 0;
            ResetTimer(wsIdleTimer);

            //var enty = new JsonEntity.MemberMessage()
            //{
            //    MsgId = e.Common.msgId,
            //    Content = $"{e.User.Nickname} 进入直播间", //直播间人数:{e.memberCount}",
            //    RoomId = e.Common.roomId,
            //    CurrentCount = e.memberCount,
            //    User = GetUser(e.User)
            //};

            var newEntity = new STTUserEnter()
            {
                Type = "uenter",
                Key = e.Common.msgId,
                UserName = e.User.Nickname,
                UserDisplayId = e.User.displayId,
                UserSecUid = e.User.sec_uid,
            };

            //Print($"{enty.Content}", ConsoleColor.Green, BarrageMsgType.进房提醒);
            //var pack = new BarrageMsgPack(JsonConvert.SerializeObject(enty), BarrageMsgType.进房提醒);
            var json = JsonConvert.SerializeObject(newEntity);
            var stream = stt.STTSerialize(json);
            this.Broadcast(stream, e.Common.roomId);
            //var logMessage = enty.Content;
            //Logger.AppendToFile(logMessage, "logs/log_stream.txt");
        }

        //点赞
        private void Grab_OnLikeMessage(object sender, ProtoEntity.LikeMessage e)
        {

            if (!CheckRoomId(e.Common.roomId)) return;
            timeoutCountOutofRange = 0;
            ResetTimer(wsIdleTimer);

            //var enty = new LikeMsg()
            //{
            //    MsgId = e.Common.msgId,
            //    Count = e.Count,
            //    Content = $"{e.User.Nickname} 为主播点了{e.Count}个赞，总点赞{e.Total}",
            //    RoomId = e.Common.roomId,
            //    Total = e.Total,
            //    User = GetUser(e.User)
            //};
            //Print($"{enty.User.GenderToString()}  {enty.Content}", ConsoleColor.Cyan, BarrageMsgType.点赞消息);
            //var pack = new BarrageMsgPack(JsonConvert.SerializeObject(enty), BarrageMsgType.点赞消息);
            //var json = JsonConvert.SerializeObject(pack);
            //this.Broadcast(json);
        }

        //弹幕
        private void Grab_OnChatMessage(object sender, ProtoEntity.ChatMessage e)
        {

            if (!CheckRoomId(e.Common.roomId)) return;
            timeoutCountOutofRange = 0;
            ResetTimer(wsIdleTimer);

            var enty = new Msg()
            {
                MsgId = e.Common.msgId,
                Content = e.Content,
                RoomId = e.Common.roomId,
                User = GetUser(e.User)
            };

            var newEntity = new STTChatMessage()
            {
                Type = "chatmsg",
                Key = e.Common.msgId,
                UserName = e.User.Nickname,
                Message = e.Content,
                UserLevel = e.User.payGrade.Level,
                UserDisplayId = e.User.displayId,
                UserSecUid = e.User.sec_uid,
            };

            var dbRecord = new ChatLog()
            {
                Sender = e.User.Nickname,
                SenderId = e.User.displayId,
                Content = e.Content,
                TimeStamp = DateTime.Now,
            };

            //Print($"{enty.User.Nickname}: {enty.Content}", ConsoleColor.White, BarrageMsgType.弹幕消息);
            //var pack = new BarrageMsgPack(JsonConvert.SerializeObject(enty), BarrageMsgType.弹幕消息);
            //var json = JsonConvert.SerializeObject(pack);
            //this.Broadcast(json);
            var json = JsonConvert.SerializeObject(newEntity);
            var stream = stt.STTSerialize(json);
            this.Broadcast(stream, e.Common.roomId);
            var logMessage = $"{enty.User.Nickname}({enty.User.DisplayId}): {enty.Content}";
            logger.LogToFile(logMessage, Logger.LogIndex.弹幕, e.Common.roomId);
            logger.LogChatToDb(dbRecord);
        }

        //直播间状态变更
        private void Grab_OnControlMessage(object sender, ControlMessage e)
        {
            if (!CheckRoomId(e.Common.roomId)) return;
            //BarrageMsgPack pack = null;
            //下播
            if (e.Status == 3)
            {
                //var enty = new Msg()
                //{
                //    MsgId = e.Common.msgId,
                //    Content = "直播已结束",
                //    RoomId = e.Common.roomId,
                //    User = null
                //};
                this.Broadcast("closed", e.Common.roomId);
                var logMessage = "直播关闭";

                logger.LogToConsole(logMessage, ConsoleColor.DarkCyan);
                logger.LogToFile(logMessage, Logger.LogIndex.程序日志);

                if (!IsCurrentHourInRange())
                {
                    if (serviceResetTimer != null && serviceResetTimer.Enabled == true) return;
                    var logMessageOutOfTimeRange = $"不在活跃时间段, 停止检测, 将于 {Appsetting.ActiveHourStart}:00 恢复正常";
                    logger.LogToConsole(logMessageOutOfTimeRange, ConsoleColor.Magenta);
                    logger.LogToFile(logMessageOutOfTimeRange, Logger.LogIndex.程序日志);
                    StopTimer(wsIdleTimer);
                    SetServiceRestartTimer();
                }
                //pack = new BarrageMsgPack(JsonConvert.SerializeObject(enty), BarrageMsgType.下播);
            }

            //    if (pack != null)
            //    {
            //        var json = JsonConvert.SerializeObject(pack);
            //        //this.Broadcast(json);
            //    }
        }

        private void Dieout_Elapsed(object sender, ElapsedEventArgs e)
        {
            var now = DateTime.Now;
            var dieoutKvs = socketList.Where(w => w.Value.LastPing.AddSeconds(dieout.Interval * 3) < now).ToList();
            dieoutKvs.ForEach(f => f.Value.Socket.Close());
        }

        private void Listen(IWebSocketConnection socket)
        {
            //客户端url
            string clientUrl = socket.ConnectionInfo.ClientIpAddress + ":" + socket.ConnectionInfo.ClientPort;
            if (!socketList.ContainsKey(clientUrl))
            {
                socketList.Add(clientUrl, new UserState(socket));
                var logMessage = $"已经建立与[{clientUrl}]的连接";
                logger.LogToConsole(logMessage, ConsoleColor.Green);
                logger.LogToFile(logMessage, Logger.LogIndex.程序日志);

                WaitForAuthentication(socket, clientUrl);
            }
            else
            {
                socketList[clientUrl].Socket = socket;
            }

            socket.OnClose = () =>
            {
                var userName = !string.IsNullOrEmpty(socketList[clientUrl].UserName) ? socketList[clientUrl].UserName : "UNKNOWN";
                socketList.Remove(clientUrl);
                var logMessage = $"已经关闭与[{clientUrl} | {userName}]的连接";
                logger.LogToConsole(logMessage, ConsoleColor.Red);
                logger.LogToFile(logMessage, Logger.LogIndex.程序日志);
            };

            socket.OnPing = (data) =>
            {
                socketList[clientUrl].LastPing = DateTime.Now;
                socket.SendPong(Encoding.UTF8.GetBytes("pong"));
            };
        }

        /// <summary>
        /// 鉴权
        /// </summary>
        /// <param name="socket"></param>
        /// <param name="clientAddr"></param>
        private void WaitForAuthentication(IWebSocketConnection socket, string clientAddr)
        {
            var timeout = TimeSpan.FromSeconds(5); // Set your desired timeout here
            var tokenReceived = false;

            var timer = new Timer(timeout.TotalMilliseconds);
            timer.Elapsed += (sender, e) =>
            {
                if (!tokenReceived)
                {
                    socket.Close();
                    var logMessage = $"{clientAddr} 认证超时, 连接关闭";
                    logger.LogToFile(logMessage, Logger.LogIndex.程序日志);
                    logger.LogToConsole(logMessage, ConsoleColor.Yellow);
                }
                timer.Dispose();
            };
            timer.Start();

            socket.OnMessage = message =>
            {

                var parts = message.Split(':');
                var token = parts[0];
                var channel = parts[1];

                var (userName, tokenPassed) = ValidateToken(token);

                if (tokenPassed)
                {
                    tokenReceived = true;

                    socketList[clientAddr].TokenHash = message;
                    socketList[clientAddr].TargetStream = long.Parse(channel);
                    socketList[clientAddr].UserName = userName;

                    timer.Stop();
                    timer.Dispose();

                    socket.Send("connected");

                    var logMessage = $"{clientAddr} | {userName} 已连接, 目标直播间: {channel}";
                    logger.LogToConsole(logMessage, ConsoleColor.Cyan);
                    logger.LogToFile(logMessage, Logger.LogIndex.程序日志);

                    // Set the main message handler after successful authentication
                    socket.OnMessage = msg =>
                    {
                        logger.LogToFile($"{clientAddr} 消息: {msg}", Logger.LogIndex.程序日志);
                    };
                }
                else
                {
                    socket.Send("rejected");
                    socket.Close();

                    timer.Stop();
                    timer.Dispose();

                    var logMessage = $"{clientAddr} 认证失败";
                    logger.LogToConsole(logMessage, ConsoleColor.Magenta);
                    logger.LogToFile(logMessage, Logger.LogIndex.程序日志);
                }
            };
        }

        /// <summary>
        /// 检查当前Token是否正在使用中
        /// </summary>
        /// <param name="hash"></param>
        /// <returns></returns>
        private bool IsHashAlreadyUsed(string hash)
        {
            return socketList.Any(kv => kv.Value.TokenHash == hash);
        }

        /// <summary>
        /// 认证Token
        /// </summary>
        /// <param name="token"></param>
        /// <returns></returns>
        private Tuple<string, bool> ValidateToken(string token)
        {
            var tokens = ReadFileAndExtractHashes();
            var res = false;
            var userName = "";
            foreach (KeyValuePair<string, string> tokenLine in tokens)
            {
                if (token == tokenLine.Key)
                {
                    if (IsHashAlreadyUsed(token))
                    {
                        InvalidateHashInFile(token);
                        var logMessage = $"认证码 {token} | {tokenLine.Value} 检测到重复使用";
                        logger.LogToConsole(logMessage, ConsoleColor.Magenta);
                        logger.LogToFile(logMessage, Logger.LogIndex.程序日志);
                        res = false;
                    }
                    else
                    {
                        userName = tokenLine.Value;
                        res = true;
                    }

                }
            }
            return Tuple.Create(userName, res);
        }

        /// <summary>
        /// 标记并注销滥用Token
        /// </summary>
        /// <param name="hashToInvalidate"></param>
        /// <param name="filePath"></param>
        private void InvalidateHashInFile(string hashToInvalidate, string filePath = "tokens.txt")
        {
            string[] lines = File.ReadAllLines(filePath);

            for (int i = 0 ; i < lines.Length ; i++)
            {
                if (lines[i].StartsWith(hashToInvalidate))
                {
                    lines[i] = $"# - 重复使用 - {lines[i]}";
                    break;
                }
            }
            File.WriteAllLines(filePath, lines);
        }

        /// <summary>
        /// 提取认证码 - TODO: Replace with Database
        /// </summary>
        /// <param name="filePath"></param>
        /// <returns></returns>
        private Dictionary<string, string> ReadFileAndExtractHashes(string filePath = "tokens.txt")
        {
            Dictionary<string, string> hashDictionary = new Dictionary<string, string>();
            Regex hashPattern = new Regex(@"^([a-fA-F\d]{32})\s-\s(.*)");

            using (StreamReader reader = new StreamReader(filePath))
            {
                string line;

                while ((line = reader.ReadLine()) != null)
                {
                    if (line.StartsWith("#"))
                    {
                        continue;
                    }

                    Match match = hashPattern.Match(line);

                    if (match.Success)
                    {
                        string md5Hash = match.Groups[1].Value;
                        string username = match.Groups[2].Value;
                        hashDictionary[md5Hash] = username.Trim();
                    }
                }
            }

            return hashDictionary;
        }

        /// <summary>
        /// 广播消息
        /// </summary>
        /// <param name="msg"></param>
        public void Broadcast<T>(T msg, long channel)
        {
            var closedSockets = new List<string>();

            foreach (var user in socketList)
            {
                var socket = user.Value;

                if (!socket.Socket.IsAvailable)
                {
                    socket.Socket.Close();
                    string clientUrl = socket.Socket.ConnectionInfo.ClientIpAddress + ":" + socket.Socket.ConnectionInfo.ClientPort;
                    closedSockets.Add(clientUrl);
                    var logMessage = $"Connection to {clientUrl} is closed, terminating";
                    logger.LogToConsole(logMessage, ConsoleColor.DarkRed);
                    logger.LogToFile(logMessage, Logger.LogIndex.程序日志);
                }
                else if (socket.TargetStream == channel)
                {
                    switch (msg)
                    {
                        case string s:
                            socket.Socket.Send(s);
                            break;
                        case byte[] bytes:
                            socket.Socket.Send(bytes);
                            break;
                    }
                }
            }

            // Remove closed sockets from the socketList
            foreach (var clientUrl in closedSockets)
            {
                socketList.Remove(clientUrl);
            }
        }

        /// <summary>
        /// 开始监听
        /// </summary>
        public void StartListen()
        {
            this.grab.Start(); //启动代理
            this.socketServer.Start(Listen);//启动监听
            Console.Title = $"抖音弹幕监听推送 [{this.socketServer.Location}]";
            var logMessage = $"{this.socketServer.Location} 弹幕服务已启动...";
            logger.LogToConsole(logMessage, ConsoleColor.White);
            logger.LogToFile(logMessage, Logger.LogIndex.程序日志);
        }

        /// <summary>
        /// 关闭服务器连接
        /// </summary>
        public void Close()
        {
            socketList.Values.ToList().ForEach(f => f.Socket.Close());
            socketList.Clear();
            socketServer.Dispose();
            grab.Dispose();
            var logMessage = $"Server Shutting Down...";
            logger.LogToFile(logMessage, Logger.LogIndex.程序日志);
        }

        class UserState
        {
            /// <summary>
            /// 套接字
            /// </summary>
            public IWebSocketConnection Socket { get; set; }

            /// <summary>
            /// 上次发起心跳包时间
            /// </summary>
            public DateTime LastPing { get; set; } = DateTime.Now;

            public string TokenHash { get; set; }

            public string UserName { get; set; }

            public long TargetStream { get; set; }

            public UserState()
            {

            }
            public UserState(IWebSocketConnection socket)
            {
                Socket = socket;
            }
        }
    }
}
