﻿// <auto-generated>
//   This file was generated by a tool; you should avoid making direct changes.
//   Consider using 'partial classes' to extend these types
//   Input: my.proto
// </auto-generated>

namespace BarrageGrab.ProtoEntity
{
    #region Designer generated code
#pragma warning disable CS0612, CS0618, CS1591, CS3021, <PERSON>E0079, IDE1006, RCS1036, RCS1057, RCS1085, RCS1192
    [global::ProtoBuf.ProtoContract()]
    public partial class WssResponse : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        [global::ProtoBuf.ProtoMember(1, Name = @"seqid")]
        public ulong Seqid { get; set; }

        [global::ProtoBuf.ProtoMember(2, Name = @"logid")]
        public ulong Logid { get; set; }

        [global::ProtoBuf.ProtoMember(3, Name = @"service")]
        public ulong Service { get; set; }

        [global::ProtoBuf.ProtoMember(4, Name = @"method")]
        public ulong Method { get; set; }

        [global::ProtoBuf.ProtoMember(5, Name = @"headers")]
        [global::ProtoBuf.ProtoMap]
        public global::System.Collections.Generic.Dictionary<string, string> Headers { get; } = new global::System.Collections.Generic.Dictionary<string, string>();

        [global::ProtoBuf.ProtoMember(6)]
        [global::System.ComponentModel.DefaultValue("")]
        public string payloadEncoding { get; set; } = "";

        [global::ProtoBuf.ProtoMember(7)]
        [global::System.ComponentModel.DefaultValue("")]
        public string payloadType { get; set; } = "";

        [global::ProtoBuf.ProtoMember(8, Name = @"payload")]
        public byte[] Payload { get; set; }

    }

#pragma warning restore CS0612, CS0618, CS1591, CS3021, IDE0079, IDE1006, RCS1036, RCS1057, RCS1085, RCS1192
    #endregion

}

