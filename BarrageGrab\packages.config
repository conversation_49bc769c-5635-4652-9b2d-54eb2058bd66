﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="BouncyCastle.Cryptography" version="2.6.1" targetFramework="net48" />
  <package id="Google.Protobuf" version="3.31.1" targetFramework="net48" />
  <package id="HtmlAgilityPack" version="1.12.1" targetFramework="net48" />
  <package id="K4os.Compression.LZ4" version="1.3.8" targetFramework="net48" />
  <package id="K4os.Compression.LZ4.Streams" version="1.3.8" targetFramework="net48" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="9.0.6" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net48" />
  <package id="Portable.BouncyCastle" version="1.9.0" targetFramework="net48" />
  <package id="protobuf-net" version="3.2.52" targetFramework="net48" />
  <package id="protobuf-net.Core" version="3.2.52" targetFramework="net48" />
  <package id="Microsoft.Data.Sqlite" version="9.0.6" targetFramework="net48" />
  <package id="SQLitePCLRaw.bundle_e_sqlite3" version="2.1.11" targetFramework="net48" />
  <package id="SQLitePCLRaw.core" version="2.1.11" targetFramework="net48" />
  <package id="SQLitePCLRaw.lib.e_sqlite3" version="2.1.11" targetFramework="net48" />
  <package id="SQLitePCLRaw.provider.dynamic_cdecl" version="2.1.11" targetFramework="net48" />
  <package id="System.Buffers" version="4.6.1" targetFramework="net48" />
  <package id="System.Collections.Immutable" version="9.0.6" targetFramework="net48" />
  <package id="System.Configuration.ConfigurationManager" version="9.0.6" targetFramework="net48" />
  <package id="System.Diagnostics.DiagnosticSource" version="9.0.6" targetFramework="net48" />
  <package id="System.IO.Pipelines" version="9.0.6" targetFramework="net48" />
  <package id="System.Memory" version="4.6.3" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.6.1" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.1.2" targetFramework="net48" />
  <package id="System.Security.AccessControl" version="6.0.1" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.6.3" targetFramework="net48" />
  <package id="ZstdSharp.Port" version="0.8.5" targetFramework="net48" />
</packages>