﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="BouncyCastle.Cryptography" version="2.2.1" targetFramework="net48" />
  <package id="BrotliSharpLib" version="0.3.3" targetFramework="net48" />
  <package id="ColorConsole" version="1.0.1" targetFramework="net48" />
  <package id="Fleck" version="1.2.0" targetFramework="net48" />
  <package id="Google.Protobuf" version="3.25.1" targetFramework="net48" />
  <package id="HtmlAgilityPack" version="1.11.59" targetFramework="net48" />
  <package id="K4os.Compression.LZ4" version="1.3.5" targetFramework="net48" />
  <package id="K4os.Compression.LZ4.Streams" version="1.3.5" targetFramework="net48" />
  <package id="K4os.Hash.xxHash" version="1.0.8" targetFramework="net48" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="5.0.0" targetFramework="net48" />
  <package id="Microsoft.Win32.Registry" version="5.0.0" targetFramework="net48" />
  <package id="MySql.Data" version="8.3.0" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.1" targetFramework="net48" />
  <package id="Portable.BouncyCastle" version="1.8.8" targetFramework="net48" />
  <package id="protobuf-net" version="3.1.22" targetFramework="net48" />
  <package id="protobuf-net.Core" version="3.1.22" targetFramework="net48" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net48" />
  <package id="System.Collections.Immutable" version="1.7.1" targetFramework="net48" />
  <package id="System.Configuration.ConfigurationManager" version="4.4.1" targetFramework="net48" />
  <package id="System.Diagnostics.DiagnosticSource" version="7.0.2" targetFramework="net48" />
  <package id="System.IO.FileSystem" version="4.3.0" targetFramework="net48" />
  <package id="System.IO.FileSystem.Primitives" version="4.3.0" targetFramework="net48" />
  <package id="System.IO.Pipelines" version="5.0.2" targetFramework="net48" />
  <package id="System.Memory" version="4.5.5" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net48" />
  <package id="System.Security.AccessControl" version="5.0.0" targetFramework="net48" />
  <package id="System.Security.Principal.Windows" version="5.0.0" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net48" />
  <package id="Titanium.Web.Proxy" version="3.2.0" targetFramework="net48" />
  <package id="ZstdSharp.Port" version="0.7.1" targetFramework="net48" />
</packages>